<template>
  <view class="h-full overflow-hidden bg-primary">
    <view class="h-full flex flex-col">
      <view class="mb-10px shrink-0">
        <wd-tabs v-model="currentInfoTypeTab" @change="onChangeInfoTypeTab">
          <wd-tab title="基础信息" />
          <wd-tab title="结算费率" />
          <wd-tab title="活动返现" />
          <wd-tab title="交易汇总" />
        </wd-tabs>
      </view>

      <view class="grow overflow-hidden">
        <scroll-view
          scroll-y :show-scrollbar="false" :scroll-top="scrollTop" :throttle="false"
          class="h-full"
        >
          <!-- ? 基础信息 -->
          <template v-if="currentInfoTypeTab === 0">
            <wd-cell-group border>
              <wd-cell title="团队名称" :value="detail.orgName" title-width="100px" />
              <wd-cell title="团队编号" :value="detail.orgNo" title-width="100px" />
              <wd-cell title="所属团队" :value="detail.teamName" title-width="100px" />
              <wd-cell title="联系人手机号" :value="detail.contactMobile ? detail.contactMobile.replace(/(\d{3})\d*(\d{4})/, '$1***$2') : ''" title-width="100px" />
            </wd-cell-group>
          </template>

          <!-- ? 结算费率 -->
          <template v-if="currentInfoTypeTab === 1">
            <view class="flex flex-col">
              <view class="mb-10px shrink-0">
                <wd-cell title="交易结算政策" :value="detail.ratePolicyName" title-width="100px" />
              </view>

              <view class="grow overflow-hidden">
                <wd-tabs v-model="currentChannelRateTab" slidable="always" custom-class="custom-rate-tabs">
                  <template v-for="(item, key) in rateMapDTOList" :key="key">
                    <wd-tab :title="item.channelName">
                      <template v-for="(rateItem, keyr) in item.rateDTOList" :key="keyr">
                        <RateModule
                          :rate-item="rateItem"
                          :readonly="true"
                        />
                      </template>
                    </wd-tab>
                  </template>
                </wd-tabs>
              </view>
            </view>
          </template>

          <!-- ? 活动返现 -->
          <template v-if="currentInfoTypeTab === 2">
            <wd-tabs v-model="activeTerminalSourceTabKey" custom-class="custom-rate-tabs">
              <wd-tab v-for="(tab, tabIndex) in policyConfigByTerminalSource || []" :key="tabIndex" :title="tab.label" :name="String(tab.key)" :lazy="false">
                <view class="gap-primary" />
                <wd-cell-group title="服务费返现规则" border />
                <template v-if="tab.serviceFeeData?.length">
                  <template v-for="(item, key) in tab.serviceFeeData" :key="key">
                    <wd-cell-group v-if="Number(item.parentCashbackAmt) > 0" border custom-class="custom-rule-group">
                      <view class="gap-primary" />
                      <wd-cell>
                        <template #title>
                          <wd-text :text="item.policyName" type="warning" />
                        </template>
                      </wd-cell>
                      <wd-cell title="返现金额" :value="`${item.cashbackAmt}元`" />
                    </wd-cell-group>
                  </template>
                </template>
                <wd-notice-bar v-else text="没有规则配置哦~" prefix="warn-bold" :scrollable="false" />

                <view class="gap-primary" />
                <wd-cell-group title="通讯费返现规则" border />
                <template v-if="tab.simFeeData?.some((s:any) => s.data.length)">
                  <view v-for="(period, idx) in tab.simFeeData" :key="idx">
                    <template v-if="period.data?.some((p:any) => Number(p.parentCashbackAmt) > 0)">
                      <view class="gap-primary" />
                      <wd-cell :title="period.name" />
                      <template v-for="(item, key) in period.data || []" :key="key">
                        <wd-cell-group v-if="Number(item.parentCashbackAmt) > 0" border custom-class="custom-rule-group">
                          <view class="h-6px bg-primary" />
                          <wd-cell>
                            <template #title>
                              <wd-text :text="item.policyName" type="warning" />
                            </template>
                          </wd-cell>
                          <wd-cell title="返现金额" :value="`${item.cashbackAmt}元`" />
                        </wd-cell-group>
                      </template>
                    </template>
                  </view>
                </template>
                <wd-notice-bar v-else text="没有规则配置哦~" prefix="warn-bold" :scrollable="false" />
              </wd-tab>
            </wd-tabs>
          </template>

          <!-- ? 交易汇总 -->
          <template v-if="currentInfoTypeTab === 3">
            <view class="px-20rpx">
              <view class="flex flex-col items-center rounded-2 bg-blue p-20rpx text-center text-white">
                <view class="flex flex-col">
                  <text>累计交易</text>
                  <text>{{ formatAmount(totalTransInfo.totalVolumeAmt) }}</text>
                </view>

                <view class="mt-10px w-full flex items-center">
                  <view class="w-50% flex flex-col">
                    <text>本月交易</text>
                    <text>{{ formatAmount(totalTransInfo.thisMonthTotalVolumeAmt) }}</text>
                  </view>
                  <view class="w-50% flex flex-col">
                    <text>上月交易</text>
                    <text>{{ formatAmount(totalTransInfo.lastMonthTotalVolumeAmt) }}</text>
                  </view>
                </view>
              </view>

              <view class="mt-10px flex p-20rpx">
                <custom-datetime-picker
                  v-model="whereDate"
                  type="date"
                  :default-value="whereDate"
                  @confirm="handleConfirmDate"
                />
              </view>
              <view class="rounded-2 bg-white py-20rpx text-center">
                <view class="flex flex-col">
                  <text>交易金额</text>
                  <text>{{ formatAmount(monthTransInfo.monthTotalVolumeAmt) }}</text>
                </view>

                <view class="mt-10px w-full flex items-center border-x-0 border-y border-slate-200 border-solid py-10px">
                  <view class="w-50% flex flex-col">
                    <text>借记卡交易</text>
                    <text>{{ formatAmount(monthTransInfo.DebitMonthTransAmt) }}</text>
                  </view>
                  <view class="w-50% flex flex-col">
                    <text>信用卡交易</text>
                    <text>{{ formatAmount(monthTransInfo.creditMonthVolumeAmt) }}</text>
                  </view>
                </view>

                <view class="mt-10px w-full flex items-center">
                  <view class="w-33.3% flex flex-col">
                    <text>微信扫码</text>
                    <text>{{ formatAmount(monthTransInfo.wxQrMonthVolumeAmt) }}</text>
                  </view>
                  <view class="w-33.3% flex flex-col">
                    <text>支付宝扫码</text>
                    <text>{{ formatAmount(monthTransInfo.aliQrMonthVolumeAmt) }}</text>
                  </view>
                  <view class="w-33.3% flex flex-col">
                    <text>云闪付交易</text>
                    <text>{{ formatAmount(monthTransInfo.unionYsfMonthVolumeAmt) }}</text>
                  </view>
                </view>
              </view>

              <view class="mt-10px rounded-2 bg-white py-20rpx text-center">
                <view class="w-full flex items-center border-0 border-b border-b-slate-200 border-solid pb-10px">
                  <view class="w-50% flex flex-col">
                    <text>机构分润收益</text>
                    <text>{{ formatAmount(monthTransInfo.orgProfitAmt) }}</text>
                  </view>
                  <view class="w-50% flex flex-col">
                    <text>机构返现收益</text>
                    <text>{{ formatAmount(monthTransInfo.orgCashbackAmt) }}</text>
                  </view>
                </view>
                <view class="mt-10px w-full flex items-center">
                  <view class="w-50% flex flex-col">
                    <text>我的分润收益</text>
                    <text>{{ formatAmount(monthTransInfo.myProfitAmt) }}</text>
                  </view>
                  <view class="w-50% flex flex-col">
                    <text>我的返现收益</text>
                    <text>{{ formatAmount(monthTransInfo.myCashbackAmt) }}</text>
                  </view>
                </view>
              </view>
            </view>
          </template>
        </scroll-view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';
import NP from 'number-precision';
import { TeamApi } from '@/api-org/team';
import { decodeUrlParams, deepClone } from '@/utils';
import { CommonApi } from '@/api/common';
import { RatePolicyApi } from '@/api-org/rate-policy';

const simFeePolicyPeriodGroupDef: any = [
  {
    name: '第一期',
    field: 'firstPeriodList',
    data: [],
  },
  {
    name: '第二期',
    field: 'secondPeriodList',
    data: [],
  },
  {
    name: '第三期',
    field: 'thirdPeriodList',
    data: [],
  },
  {
    name: '标准期 (第四期以及后续阶段)',
    field: 'fourthPeriodList',
    data: [],
  },
];

const policyConfigByTerminalSourceDef: any = [
  {
    label: '全款机配置',
    key: 1,
    serviceFeeData: [],
    simFeeData: [],
  },
  {
    label: '分期机配置',
    key: 2,
    serviceFeeData: [],
    simFeeData: [],
  },
];

type RouteParams = {
  orgCode: string;
  orgType: string;
};

// 路由参数
let routeParams: RouteParams;

const scrollTop = ref(0);

const currentInfoTypeTab = ref(0);

const currentChannelRateTab = ref(0);

// 详情信息
const detail = ref<any>({});

// 费率
const rateMapDTOList = ref<any>([]);

const activeTerminalSourceTabKey = ref('1');
const policyConfigByTerminalSource = ref<any>([]);

// 交易汇总
const totalTransInfo = ref<any>({});

// 月交易汇总
const monthTransInfo = ref<any>({});

// 月交易汇总-查询时间范围
const whereStartDateDef = dayjs().startOf('month').valueOf();
const whereEndDateDef = dayjs().valueOf();
const whereDate = ref([whereStartDateDef, whereEndDateDef]);
const monthTransInfoWhere = reactive({
  selTimeType: 1,
  queryBeginTime: dayjs(whereStartDateDef).format('YYYY-MM-DD'), // 开始时间 yyyy-MM-dd
  queryEndTime: dayjs(whereEndDateDef).format('YYYY-MM-DD'), // 结束时间 yyyy-MM-dd
});

function handleConfirmDate({ value }: { value: string[] }) {
  [monthTransInfoWhere.queryBeginTime, monthTransInfoWhere.queryEndTime] = value;
  queryMonthTransVolume();
}

const oriPolicyDTOListMap: any = {
  serviceFeePolicyDTOList: [],
  simFeeNewPolicyDTO: {},
};

onLoad(async (query: any) => {
  query = decodeUrlParams(query);
  routeParams = Object.assign({}, query);
  await queryAddDisplay();
  getAgentDetail();
});

function onChangeInfoTypeTab({ index }: { index: number }) {
  scrollTop.value = 0;

  // 切换到交易汇总
  if (index === 3) {
    queryTotalTransVolume();
    queryMonthTransVolume();
  }
}

async function getAgentDetail() {
  const data = await TeamApi.queryAgentDetail({
    orgCode: routeParams.orgCode,
    orgType: routeParams.orgType,
  });
  detail.value = Object.assign({}, data);

  const serviceFeePolicyDTOMap: any = {};
  const simFeePolicyDTOMap: any = {};
  // 自身全部服务费
  oriPolicyDTOListMap.serviceFeePolicyDTOList.forEach((s: any) => {
    serviceFeePolicyDTOMap[s.configId] = s;
  });

  // 自身全部流量费
  Object.keys(oriPolicyDTOListMap.simFeeNewPolicyDTO || {}).forEach((key) => {
    simFeePolicyDTOMap[key] = Object.assign({}, simFeePolicyDTOMap[key] || {});
    oriPolicyDTOListMap.simFeeNewPolicyDTO[key].forEach((s: any) => {
      simFeePolicyDTOMap[key][s.configId] = s;
    });
  });

  // 规则反显
  const { serviceFeePolicyDTOList, simFeeNewPolicyDTO } = data || {};

  const policyConfigByTerminalSourceMap = deepClone(policyConfigByTerminalSourceDef);

  const serviceFeePolicyDTOListMap = serviceFeePolicyDTOList || [];
  serviceFeePolicyDTOListMap.forEach((i: any) => {
    const configItem = serviceFeePolicyDTOMap[i.configId] || {};
    i.parentCashbackAmt = configItem.parentCashbackAmt;
  });

  const simFeePolicyMap = simFeeNewPolicyDTO || {};
  const simFeePolicyPeriodGroupMap = deepClone(simFeePolicyPeriodGroupDef);
  simFeePolicyPeriodGroupMap.forEach((period: any) => {
    Object.keys(simFeePolicyMap).forEach((key) => {
      if (period.field === key) {
        period.data = simFeePolicyMap[key] || [];
        period.checkedList = [];
        period.data.forEach((item: any) => {
          const configItem = (simFeePolicyDTOMap[key] && simFeePolicyDTOMap[key][item.configId]) || {};
          item.parentCashbackAmt = configItem.parentCashbackAmt;
        });
      }
    });
  });

  policyConfigByTerminalSourceMap.forEach((tab: any) => {
    tab.serviceFeeData = serviceFeePolicyDTOListMap.filter((item: any) => item.terminalSource === tab.key);

    const simFeeData = simFeePolicyPeriodGroupMap.map((period: any) => {
      return {
        ...period,
        data: period.data.filter((item: any) => item.terminalSource === tab.key),
      };
    });
    tab.simFeeData = simFeeData;
  });

  policyConfigByTerminalSource.value = policyConfigByTerminalSourceMap;

  // 费率反显
  const channels: any = await getChannelList();

  const rateMap = data.rateMap || {};
  const setedChannel = Object.keys(rateMap);

  rateMapDTOList.value = [];

  setedChannel.forEach((channelCode) => {
    const channel = channels.find((item: any) => item.channelCode === channelCode);
    rateMapDTOList.value.push({
      channelCode,
      channelName: channel?.channelName || channelCode,
      rateDTOList: rateMap[channelCode],
    });
  });
}

/**
 * 查询自身返现政策
 */
async function queryAddDisplay() {
  const data = await RatePolicyApi.queryAddDisplay();
  let { serviceFeePolicyDTOList, simFeeNewPolicyDTO } = data || {};
  serviceFeePolicyDTOList = serviceFeePolicyDTOList || [];
  simFeeNewPolicyDTO = simFeeNewPolicyDTO || {};

  oriPolicyDTOListMap.serviceFeePolicyDTOList = deepClone(serviceFeePolicyDTOList);
  oriPolicyDTOListMap.simFeeNewPolicyDTO = deepClone(simFeeNewPolicyDTO);
}

async function queryTotalTransVolume() {
  const data = await TeamApi.queryTotalTransVolume({
    orgNo: routeParams.orgCode,
    orgType: routeParams.orgType,
  });
  totalTransInfo.value = Object.assign({}, data);
}

async function queryMonthTransVolume() {
  const data = await TeamApi.queryMonthTransVolume({
    orgNo: routeParams.orgCode,
    orgType: routeParams.orgType,
    ...monthTransInfoWhere,
  });
  monthTransInfo.value = Object.assign({}, data);
}

async function getChannelList() {
  const data = await CommonApi.getChannelList({});
  return Promise.resolve(data || []);
}

/**
 * 金额转换 保留两位小数
 */
function formatAmount(amount: number) {
  if (!amount)
    return 0;
  amount = Number(amount);
  return NP.round(amount, 2);
}
</script>

<style lang="scss" scoped>
:deep(.custom-rule-group){
  background-color: transparent;

  .wd-cell-group__body{
    background-color: transparent;
  }
}

:deep(.custom-rate-tabs){
  height: 100%;

  .wd-tabs__container{
    overflow-y: scroll;
    height: calc(100% - 42px);
  }
}
</style>
