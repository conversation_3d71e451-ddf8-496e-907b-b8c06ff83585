<template>
  <view class="h-full bg-primary">
    <page-paging ref="pagingRef" v-model="datasource" @query="queryDataSource">
      <!-- 头部搜索栏 -->
      <template #top>
        <wd-navbar
          left-arrow :bordered="false" safe-area-inset-top custom-class="custom-navbar-class"
          @click-left="handleBack"
        >
          <template #title>
            <view class="search-box">
              <wd-search
                placeholder="请输入团队名称搜索"
                hide-cancel placeholder-left custom-class="w-full"
                @search="onSearchByOrgName"
              />
            </view>
          </template>
        </wd-navbar>

        <view class="bg-white p-20rpx">
          <view class="mb-10rpx font-500">
            团队汇总
          </view>
          <view class="w-full flex items-center rounded-2 bg-primary p-20rpx">
            <view class="w-33.3% flex flex-col items-center">
              <text>运营中心</text>
              <text class="mt-10rpx">
                {{ orgSummary.branchCount }}
              </text>
            </view>
            <view class="lr-border w-33.3% flex flex-col items-center">
              <text>一级代理</text>
              <text class="mt-10rpx">
                {{ orgSummary.oneLevelAgentCount }}
              </text>
            </view>
            <view class="w-33.3% flex flex-col items-center">
              <text>个人代理</text>
              <text class="mt-10rpx">
                {{ orgSummary.subAgentCount }}
              </text>
            </view>
          </view>
        </view>

        <!-- 机构类型 tabs -->
        <view class="mt-20rpx">
          <wd-tabs v-model="currentOrgTab" @change="onChangeOrgTab">
            <wd-tab title="运营中心" name="1" />
            <wd-tab title="代理商" name="2" />
          </wd-tabs>
        </view>
      </template>

      <!-- 不同机构类型查询条件不同 -->
      <view class="mt-20rpx bg-white">
        <template v-if="currentOrgTab === '1'">
          <wd-drop-menu custom-class="flex">
            <wd-drop-menu-item v-model="whereRatePolicy" :[ratePolicyTitleProp]="'政策筛选'" :options="ratePolicyOptions" @change="onChangeWhereRatePolicy" />
          </wd-drop-menu>
        </template>

        <template v-else-if="currentOrgTab === '2'">
          <wd-search
            hide-cancel placeholder-left placeholder="请输入运营中心编号查询" custom-class="w-full"
            @search="onSearchByOrgCode"
          />
        </template>
      </view>

      <!-- 列表 -->
      <view class="p-20rpx">
        <view
          v-for="(item, key) in datasource" :key="key"
          class="mb-20rpx rounded-xl bg-white p-26rpx shadow"
          @click="onClickItem(item)"
        >
          <view>
            <!-- ? 运营中心 -->
            <template v-if="currentOrgTab === '1'">
              <view class="flex items-center font-bold">
                {{ item.branchName }}
                <i
                  v-if="item.branchName"
                  class="copy-icon"
                  @click.stop="copyData(item.branchName)"
                />
              </view>
              <view class="mt-20rpx flex items-center">
                <view class="grow">
                  <view class="cell">
                    <text class="cell-label">
                      用户编号:
                    </text>
                    <view class="cell-value flex items-center">
                      {{ item.branchNo }}
                      <i
                        v-if="item.branchNo"
                        class="copy-icon"
                        @click.stop="copyData(item.branchNo)"
                      />
                    </view>
                  </view>
                  <view class="cell">
                    <text class="cell-label">
                      加入时间:
                    </text>
                    <text class="cell-value">
                      {{ item.createTimeStr }}
                    </text>
                  </view>
                </view>
                <i class="i-mdi-chevron-right shrink-0 text-50rpx text-#00000040" />
              </view>

              <!-- 结算政策描述 -->
              <view class="mt-8px">
                <wd-tag custom-class="custom-tag-class" mark>
                  {{ item.policyDesc || '--' }}
                </wd-tag>
              </view>
            </template>

            <!-- ? 代理商 -->
            <template v-else-if="currentOrgTab === '2'">
              <view class="flex items-center font-bold">
                {{ item.agentName }}
                <i
                  v-if="item.agentName"
                  class="copy-icon"
                  @click.stop="copyData(item.agentName)"
                />
              </view>
              <view class="mt-20rpx flex items-center">
                <view class="grow">
                  <view class="cell">
                    <text class="cell-label">
                      用户编号:
                    </text>
                    <view class="cell-value flex items-center">
                      {{ item.agentNo }}
                      <i
                        v-if="item.agentNo"
                        class="copy-icon"
                        @click.stop="copyData(item.agentNo)"
                      />
                    </view>
                  </view>
                  <view class="cell">
                    <text class="cell-label">
                      所属运营中心:
                    </text>
                    <view class="cell-value flex items-center">
                      {{ item.teamOrgName }}
                      <i
                        v-if="item.teamOrgName"
                        class="copy-icon"
                        @click.stop="copyData(item.teamOrgName)"
                      />
                    </view>
                  </view>
                  <view class="cell">
                    <text class="cell-label">
                      加入时间:
                    </text>
                    <text class="cell-value">
                      {{ item.createTime }}
                    </text>
                  </view>
                  <view v-if="item.checkStatus !== 3 " class="cell">
                    <text class="cell-label">
                      入网状态:
                    </text>
                    <text class="cell-value">
                      {{ checkStatusMap[item.checkStatus] || '--' }}
                    </text>
                  </view>
                </view>
                <i class="i-mdi-chevron-right shrink-0 text-50rpx text-#00000040" />
              </view>
            </template>

            <!-- 其他信息 -->
            <view class="mt-20rpx rounded-2 bg-primary p-20rpx text-28rpx">
              <view class="flex items-start">
                <template v-if="currentOrgTab === '1'">
                  <view class="w-50%">
                    <text>商户数:</text>
                    <text>{{ item.merchantCount }}</text>
                  </view>
                  <view class="w-50%">
                    <text>团队数:</text>
                    <text>{{ item.agentCount }}</text>
                  </view>
                </template>
                <template v-else-if="currentOrgTab === '2'">
                  <view class="w-37%">
                    <text>商户数:</text>
                    <text>{{ item.merchantCount }}</text>
                  </view>
                  <view class="w-37%">
                    <text>终端数:</text>
                    <text>{{ item.snCount }}</text>
                  </view>
                  <view class="w-26%">
                    <text>团队数:</text>
                    <text>{{ item.agentCount }}</text>
                  </view>
                </template>
              </view>
              <view v-if="currentOrgTab === '2'" class="mt-10rpx flex items-start">
                <view class="w-50%">
                  <text>本月交易量:</text>
                  <text>{{ item.thisMonthTransAmt }}</text>
                </view>
                <view class="w-50%">
                  <text>上月交易量:</text>
                  <text>{{ item.lastMonthTransAmt }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部 -->
      <template #bottom>
        <view class="bg-white px-40rpx pb-40rpx pt-20rpx">
          <wd-button type="primary" size="large" block @click="handleAddBranch">
            新增运营中心
          </wd-button>
        </view>
      </template>
    </page-paging>
  </view>
</template>

<script setup lang="ts">
import { useClipboard } from '@/hooks';
import { TeamApi } from '@/api-org/team';
import { RatePolicyApi } from '@/api-org/rate-policy';
import { buildUrlWithParams } from '@/utils';

// 分页器ref
const pagingRef = ref();

// 运营列表查询条件
const findBranchWhere = reactive<any>({
  ratePolicyId: null, // 费率政策ID 根据政策筛选上送ID（选填）
});

// 代理商列表查询条件
const findAgentWhere = reactive<any>({
  queryMethod: 0, // 查询方式（0-一级代理 1-直属 2-非直属）
  orgNo: '', // 机构编号（选填）
});

// 公共查询条件
const findCommonWhere = reactive<any>({
  // 企业名称（选填）
  orgName: '',
  // #region 排序方式 不填默认ID倒序
  // 本月交易量：10-正序 11-倒序
  // 上月交易量：20-正序 21-倒序
  // 商户数量：30-正序 31-倒序
  // 商户激活数：40-正序 41-倒序
  // 加入时间：50-正序 51-倒序
  // 设备数：60-正序 61-倒序
  // #endregion
  orderBy: null,
});

// 费率政策筛选 只有运营中心有 0-全部
const whereRatePolicy = ref('0');
const ratePolicyOptions = ref<any>([]);
function onChangeWhereRatePolicy({ value }: { value: string }) {
  findBranchWhere.ratePolicyId = value === '0' ? null : whereRatePolicy.value;
  pagingRef.value.reload();
}

const ratePolicyTitleProp = computed(() => {
  const prop = whereRatePolicy.value === '0' ? 'title' : '';
  return prop;
});

// 团队汇总信息
const orgSummary = ref<any>({
  branchCount: '0', // 运营中心总数
  oneLevelAgentCount: '0', // 一级代理总数
  subAgentCount: '0', // 个人代理总数
});

// 机构类型Tabs
const currentOrgTab = ref('1');
function onChangeOrgTab() {
  pagingRef.value.reload();
}

// 列表数据
const datasource = ref<Record<string, any>[]> ([]);

const checkStatusMap: EnumMap = {
  3: '正常',
  4: '审核中',
  5: '驳回',
};

onShow(() => {
  queryOrgSummary();
  queryRatePolicy();
  pagingRef.value?.reload();
});

/**
 * 查询团队汇总信息
 */
async function queryOrgSummary() {
  const data = await TeamApi.queryOrgSummary();
  orgSummary.value = Object.assign({}, data);
}

/**
 * 查询费率政策
 */
async function queryRatePolicy() {
  let data = await RatePolicyApi.listOwnerPolicy();
  data = data || [];
  data.unshift({
    id: '0',
    policyDesc: '全部',
  });

  const formatData = data.map((item: any) => {
    return {
      label: item.policyDesc,
      value: item.id,
    };
  });

  ratePolicyOptions.value = formatData || [];
}

/** 搜索查询数据 */
function onSearchByOrgName({ value }: any) {
  findCommonWhere.orgName = value;
  pagingRef.value?.reload();
}

/** 搜索查询数据 */
function onSearchByOrgCode({ value }: any) {
  findAgentWhere.orgNo = value;
  pagingRef.value?.reload();
}

function copyData(data: string) {
  useClipboard().setClipboardData({ data });
}

/**
 * 查询分页数据
 */
function queryDataSource(pageNo: number, pageSize: number) {
  datasource.value = [];

  let result;

  const commonParams = {
    ...findCommonWhere,
    pageNo,
    pageSize,
  };

  switch (currentOrgTab.value) {
    // 运营中心
    case '1':
      result = TeamApi.findOrgBranchPage({ ...commonParams, ...findBranchWhere });
      break;
    // 代理商
    case '2':
      result = TeamApi.findOrgAgentPage({ ...commonParams, ...findAgentWhere });
      break;
  }

  result!
    .then((res) => {
      pagingRef.value.completeByTotal(res?.rows, res?.totalRows);
    })
    .catch(() => {
      pagingRef.value.completeByTotal(false);
    });
}

/**
 * 点击列表项
 */
function onClickItem(item: any) {
  let url: string = '';

  switch (currentOrgTab.value) {
    // 运营中心
    case '1':
      url = buildUrlWithParams('/pages-org/team/branch/branch-detail', {
        orgCode: item.branchNo,
        orgType: item.orgType,
      });
      break;
    // 代理商
    case '2':
      url = buildUrlWithParams('/pages-org/team/agent/agent-detail', {
        orgCode: item.agentNo,
        orgType: item.orgType,
      });
      break;
  }

  uni.navigateTo({ url });
}

function handleAddBranch() {
  uni.navigateTo({ url: '/pages-org/team/branch/branch-update' });
}

function resubmitMerch(item: any) {
  uni.navigateTo({ url: `/pages/report/merch-dredge/index?handleType=3&id=${item.id}&isMicro=${item.isMicro}` });
}

function handleBack() {
  uni.navigateBack();
}
</script>

<style lang="scss" scoped>
.search-box {
  display: flex;
  align-items: center;
  height: 100%;
  text-align: left;

  --wot-search-padding: 0;
  --wot-search-side-padding: 0;
}

.cell{
    @apply flex items-center;

    &:not(:last-child){
     @apply mb-8rpx
    }

     .cell-label{
      @apply shrink-0;
     }

     .cell-value{
      @apply grow ml-20rpx flex items-center text-#333;
     }
  }

:deep(.custom-tag-class){
  margin-right: 10rpx;
  font-size: 26rpx !important;
  color: #4d80f0 !important;
  background: #d0e8ff !important;
}

:deep(.custom-navbar-class){
  padding-top: 8px;

  .wd-navbar__title{
    padding: 0 12px 0 44px;
    margin: 0;
    max-width: 100%;
  }
}

.lr-border{
  position: relative;
  overflow: hidden;

  &::before,
  &::after{
   position: absolute;
   top: 0;
   width: 1px;
   height:60%;
   background: #e6e6e6;
   content: '';
   transform: translateY(50%);
  }

  &::before{
   left: 0;

  }

  &::after{
   right: 0;
  }

}

.copy-icon{
  @apply i-mdi-content-copy ml-10rpx size-28rpx text-#b51e1e
}
</style>
