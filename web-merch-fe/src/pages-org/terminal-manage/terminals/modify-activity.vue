<template>
  <view class="h-full overflow-scroll bg-primary">
    <view class="mt-10px" />
    <wd-form ref="formRef" :model="form">
      <wd-cell-group title="激活配置(首笔)" border>
        <wd-cell title-width="0" custom-class="custom-radios-cell" prop="serviceFeeId" :rules="[{ required: true, message: '请选择' }]">
          <wd-radio-group v-model="form.serviceFeeId" shape="dot" inline>
            <wd-radio v-for="(item, key) in serviceFeePolicyOptions" :key="key" :value="item.configId">
              {{ item.policyName }}激活
            </wd-radio>
          </wd-radio-group>
        </wd-cell>
      </wd-cell-group>

      <view class="mt-10px" />
      <wd-cell-group title="通讯费配置" border>
        <wd-row>
          <wd-col :span="12">
            <wd-cell title="通讯费收取开关" title-width="110px" center custom-class="custom-switch-cell" prop="simSwitch" :rules="[{ required: true, message: '请选择' }]">
              <wd-switch v-model="form.simSwitch" :active-value="1" :inactive-value="0" size="24px" />
            </wd-cell>
          </wd-col>
          <wd-col v-if="form.simSwitch" :span="12">
            <wd-cell title="免费天数" title-width="80px" center prop="simFreeDay" :rules="[{ required: true, message: '请填写' }]">
              <wd-input v-model="form.simFreeDay" type="number" placeholder="请输入">
                <template #suffix>
                  天
                </template>
              </wd-input>
            </wd-cell>
          </wd-col>
        </wd-row>

        <template v-if="form.simSwitch">
          <template v-for="(item, key) in form.simFeePeriodConfigDTO || []" :key="key">
            <view class="h-10rpx bg-primary" />

            <wd-cell-group :title="item.periodName" border>
              <wd-row custom-class="flex justify-between">
                <wd-col :span="12">
                  <wd-picker
                    v-model="item[item.periodIdField]"
                    :prop="`simFeePeriodConfigDTO.${key}.${item.periodIdField}`"
                    :rules="[{ required: true, message: '请选择' }]"
                    label="金额"
                    label-width="50px"
                    label-key="policyName"
                    value-key="configId"
                    placeholder="请选择"
                    :columns="simFeePolicyOptionsMap[item.periodIdOptionsField] || []"
                  />
                </wd-col>
                <wd-col :span="12">
                  <wd-picker
                    v-model="item[item.periodDayField]"
                    :prop="`simFeePeriodConfigDTO.${key}.${item.periodDayField}`"
                    :rules="[{ required: true, message: '请选择' }]"
                    label="权益天数"
                    label-width="70px"
                    placeholder="请选择"
                    :columns="periodDayOptions"
                  />
                </wd-col>
              </wd-row>
            </wd-cell-group>
          </template>
        </template>
      </wd-cell-group>

      <view class="mt40px px-40rpx">
        <wd-button type="primary" size="large" block @click="save">
          确定
        </wd-button>
      </view>
    </wd-form>

    <!-- 挂载点 -->
    <wd-toast />
  </view>
</template>

<script setup lang="ts">
import type { FormInstance } from 'wot-design-uni/components/wd-form/types';
import { useToast } from 'wot-design-uni';
import { TerminalManageApi } from '@/api-org/terminal-manage';
import { NavigationHelper, deepClone } from '@/utils';

const toast = useToast();

// 权益天数配置项
const periodDayOptions: any = [180, 210, 240, 270, 300, 360];
// 周期流量费数据结构
const simFeePeriodConfigDef: any = [
  {
    periodName: '第一期',
    periodIdField: 'firstSimFeeId',
    periodFeeField: 'firstSimFee',
    periodDayField: 'firstSimPeriodDay',
    periodIdOptionsField: 'firstPeriodList',
    firstSimFeeId: null,
    firstSimPeriodDay: null,
  },
  {
    periodName: '第二期',
    periodIdField: 'secondSimFeeId',
    periodFeeField: 'secondSimFee',
    periodDayField: 'secondSimPeriodDay',
    periodIdOptionsField: 'secondPeriodList',
    secondSimFeeId: null,
    secondSimPeriodDay: null,
  },
  {
    periodName: '第三期',
    periodIdField: 'thirdSimFeeId',
    periodFeeField: 'thirdSimFee',
    periodDayField: 'thirdSimPeriodDay',
    periodIdOptionsField: 'thirdPeriodList',
    thirdSimFeeId: null,
    thirdSimPeriodDay: null,
  },
  {
    periodName: '第四期',
    periodIdField: 'fourthSimFeeId',
    periodFeeField: 'fourthSimFee',
    periodDayField: 'fourthSimPeriodDay',
    periodIdOptionsField: 'fourthPeriodList',
    fourthSimFeeId: null,
    fourthSimPeriodDay: null,
  },
];

// 表单
const formRef = ref<FormInstance | null>(null);
const form = reactive<any>({
  simSwitch: 1,
});

const serviceFeePolicyOptions = ref<any>([]);
const simFeePolicyOptionsMap = ref<any>({});

onLoad((query: any) => {
  const { transferredData, hasTransferredData } = NavigationHelper.getTransferredData(query);

  // 初始化反显数据
  if (hasTransferredData) {
    initReverseForm(transferredData);
  }

  getServiceFeePolicyList();
  getSimFeePolicyList();
});

function initReverseForm(data: any) {
  Object.assign(form, data);

  const simFeePeriodConfigDTO = deepClone(simFeePeriodConfigDef);
  simFeePeriodConfigDTO.forEach((item: any) => {
    item[item.periodIdField] = form[item.periodIdField] || null;
    item[item.periodDayField] = form[item.periodDayField] || null;
  });
  form.simFeePeriodConfigDTO = simFeePeriodConfigDTO;
}

async function save() {
  // 检验表单
  const { valid, errors } = await formRef.value!.validate();
  if (!valid)
    return Promise.reject(errors);

  const serviceFeeItem: any = serviceFeePolicyOptions.value.find((item: any) => item.configId === form.serviceFeeId);

  const params: any = {
    channelCode: form.channelCode,
    ids: form.ids,
    serviceFeeId: form.serviceFeeId,
    serviceFee: serviceFeeItem?.amount,
    simSwitch: form.simSwitch,
    terminalSource: form.terminalSource,
  };

  if (form.simSwitch) {
    params.simFreeDay = form.simFreeDay;
    form.simFeePeriodConfigDTO.forEach((item: any) => {
      params[item.periodIdField] = item[item.periodIdField];
      params[item.periodDayField] = item[item.periodDayField];

      const simFeeItem: any = simFeePolicyOptionsMap.value[item.periodIdOptionsField].find((feeItem: any) => feeItem.configId === item[item.periodIdField]);
      params[item.periodFeeField] = simFeeItem?.simFeeAmt;
    });
  }

  await TerminalManageApi.modifyActivity(params);

  toast.success({
    msg: '操作成功',
    closed: () => {
      uni.navigateBack();
    },
  });
}

/**
 * 获取服务政策列表
 */
async function getServiceFeePolicyList() {
  const data = await TerminalManageApi.serviceFeePolicySelfOpenList();
  serviceFeePolicyOptions.value = data || [];
}

/**
 * 获取奖励政策列表
 */
async function getSimFeePolicyList() {
  const data = await TerminalManageApi.simFeePolicySelfOpenList();
  simFeePolicyOptionsMap.value = data || {};
}
</script>

<style lang="scss" scoped>
:deep(.custom-radios-cell) {
  .wd-cell__value{
    text-align: left;
  }

  .wd-radio-group{
    display: flex;
    flex-wrap: wrap;
  }

}

:deep(.custom-switch-cell) {
  .wd-cell__value{
    display: flex;
  }
}
</style>
