<template>
  <view class="h-full overflow-hidden bg-primary">
    <page-paging ref="pagingRef" v-model="datasource" default-page-size="30" @query="queryDataSource">
      <!-- 头部 -->
      <template #top>
        <wd-search
          hide-cancel placeholder-left placeholder="请输入机具SN号" custom-class="w-full"
          @search="onSearchByTerminalSn"
        />

        <view class="flex items-center px-30rpx">
          <view class="grow">
            共{{ totalDataCount }}台
          </view>
          <view class="ml-20rpx shrink-0">
            <wd-drop-menu custom-class="custom-drop-menu-class">
              <wd-drop-menu-item
                v-model="where.modelId" title="筛选" @change="reload"
              />
            </wd-drop-menu>
          </view>
        </view>

        <wd-drop-menu>
          <wd-drop-menu-item
            v-model="where.modelId" :options="modelIdOptions" :[modelIdTitleProp]="'机具型号'"
            @change="reload"
          />
          <wd-drop-menu-item
            v-model="where.serviceFeeId" :options="serviceFeeIdOptions" :[serviceFeeIdTitleProp]="'激活政策'"
            @change="reload"
          />
        </wd-drop-menu>
      </template>

      <!-- 列表 -->
      <view class="mt-10px">
        <wd-checkbox-group v-model="form.terminalIdList" cell border>
          <view v-for="(item, key) in datasource" :key="key" class="flex items-center border-t-#f8f8f8 border-t-solid">
            <wd-checkbox :model-value="item.id">
              SN: {{ item.terminalSn }}
            </wd-checkbox>
            <i
              v-if="item.terminalSn"
              class="copy-icon"
              @click.stop="copyData(item.terminalSn)"
            />
          </view>
        </wd-checkbox-group>
      </view>

      <!-- 底部 -->
      <template #bottom>
        <view class="border-t-#f8f8f8 border-t-solid bg-white px-30rpx pb-40rpx pt-20rpx">
          <view class="flex items-center">
            <view class="shrink-0">
              <wd-checkbox v-model="isCheckedAll">
                全选
              </wd-checkbox>
            </view>
            <view class="mx-20rpx grow break-all text-center text-#4d80f0">
              已选{{ form.terminalIdList?.length || 0 }}台
            </view>
            <view class="flex shrink-0 items-center">
              <wd-button size="small" @click="handleModifyActivity">
                修改活动
              </wd-button>
            </view>
          </view>
        </view>
      </template>
    </page-paging>

    <!-- 挂载点 -->
    <wd-message-box />
  </view>
</template>

<script setup lang="ts">
import { useMessage } from 'wot-design-uni';
import { useClipboard } from '@/hooks';
import { NavigationHelper, buildUrlWithParams, deepClone } from '@/utils';
import { TerminalManageApi } from '@/api-org/terminal-manage';

const message = useMessage();

// 分页器ref
const pagingRef = ref();

// 列表查询条件
const where = reactive<any>({
  terminalSn: '', // 终端序列号
  modelId: -1, // 型号ID
  serviceFeeId: -1, // 服务费率政策id
  terminalSnStart: '', // 终端开始序列号(terminalSnStart terminalSnEnd 同时必填)
  terminalSnEnd: '', // 终端开始序列号(terminalSnStart terminalSnEnd 同时必填)
});

// 列表数据
const datasource = ref<Record<string, any>[]> ([]);
const totalDataCount = ref<number>(0);

const form = reactive<any>({
  terminalIdList: [],
});

// 全选/反选
const isCheckedAll = computed({
  get() {
    return form.terminalIdList.length && form.terminalIdList.length === datasource.value.length;
  },
  set(val) {
    if (val) {
      form.terminalIdList = datasource.value.map((item: any) => item.id);
    }
    else {
      form.terminalIdList = [];
    }
  },
});

const serviceFeeIdOptions = ref<any>([
  { label: '全部', value: -1 },
]);
const serviceFeeIdTitleProp = computed(() => {
  const prop = where.serviceFeeId === -1 ? 'title' : '';
  return prop;
});

const modelIdOptions = ref<any>([
  { label: '全部', value: -1 },
]);
const modelIdTitleProp = computed(() => {
  const prop = where.modelId === -1 ? 'title' : '';
  return prop;
});

onLoad(() => {
  queryServiceFee();
  queryModels();
});

function handleModifyActivity() {
  if (!form.terminalIdList.length) {
    message.alert({
      msg: '请选择终端',
      title: '提示',
    });
    return;
  }

  const findTermianlList = datasource.value.filter((item: any) => form.terminalIdList.includes(item.id));
  // 判断通道编号是否相同
  const channelNoSet = new Set(findTermianlList.map((item: any) => item.channelCode));
  if (channelNoSet.size > 1) {
    message.alert({
      msg: '请选择相同通道的终端',
      title: '提示',
    });
    return;
  }

  NavigationHelper.navigateToWithData('/pages-org/terminal-manage/terminals/modify-activity', {
    ids: form.terminalIdList,
    channelCode: findTermianlList[0]?.channelCode,
    terminalSource: findTermianlList[0]?.terminalSource,
  });
}

async function queryModels() {
  let data = await TerminalManageApi.terminalModelList({});
  data = data || [];

  const formatData = data.map((item: any) => {
    return {
      label: item.modelName,
      value: item.id,
    };
  });
  modelIdOptions.value = [modelIdOptions.value[0], ...formatData];
}

async function queryServiceFee() {
  let data = await TerminalManageApi.serviceFeePolicySelfOpenList();
  data = data || [];

  const formatData = data.map((item: any) => {
    return {
      label: item.policyName,
      value: item.configId,
    };
  });
  serviceFeeIdOptions.value = [serviceFeeIdOptions.value[0], ...formatData];
}

/** 搜索数据 */
function onSearchByTerminalSn({ value }: any) {
  where.terminalSn = value;
  reload();
}

function reload() {
  form.terminalIdList = [];
  pagingRef.value?.reload();
}

function copyData(data: string) {
  useClipboard().setClipboardData({ data });
}

/** 查询数据 */
function queryDataSource(pageNo: number, pageSize: number) {
  const formatWhere = deepClone(where);

  formatWhere.modelId = formatWhere.modelId === -1 ? null : formatWhere.modelId;
  formatWhere.serviceFeeId = formatWhere.serviceFeeId === -1 ? null : formatWhere.serviceFeeId;

  TerminalManageApi.selefIdleTerminal({ ...formatWhere, pageNo, pageSize })
    .then((res) => {
      pagingRef.value.completeByTotal(res?.rows, res?.totalRows);
      totalDataCount.value = res?.totalRows || 0;
    })
    .catch(() => {
      pagingRef.value.completeByTotal(false);
    });
}
</script>

<style lang="scss" scoped>
:deep(.custom-drop-menu-class){
  .wd-drop-menu__list{
    background-color: transparent;
  }
}

.copy-icon{
  @apply i-mdi-content-copy ml--5px size-28rpx text-#b51e1e
}
</style>
