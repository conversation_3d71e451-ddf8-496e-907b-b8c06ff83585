<template>
  <view class="h-full flex flex-col bg-primary">
    <view class="shrink-0">
      <view class="gap-primary" />
      <wd-form ref="formRef" :model="form" :rules="rules" custom-class="error-message__align-right">
        <wd-cell-group>
          <wd-input v-model="form.activityCashbackPolicyName" label="活动返现模板名称" placeholder="请输入模板名称" prop="activityCashbackPolicyName" label-width="130px" clearable align-right />
        </wd-cell-group>
      </wd-form>
      <view class="gap-primary" />
    </view>

    <view class="grow overflow-y-scroll">
      <wd-form ref="ruleFormRef" :model="ruleForm">
        <wd-checkbox-group v-model="checkedServiceRules" custom-class="custom-checkbox-group">
          <wd-tabs v-model="activeChannelTabKey" custom-class="custom-rate-tabs" slidable="always">
            <wd-tab v-for="(channel, cIndex) in ruleForm.policyConfigByChannelGroup || []" :key="cIndex" :title="channel.channelName" :name="String(cIndex)" :lazy="false">
              <wd-tabs v-model="activeTerminalSourceTabKey" custom-class="custom-rate-tabs" slidable="always">
                <wd-tab v-for="(tab, tabIndex) in channel.channelData || []" :key="tabIndex" :title="tab.label" :name="String(tab.key)" :lazy="false">
                  <view class="gap-primary" />
                  <wd-cell-group title="服务费返现规则" border />
                  <template v-if="tab.serviceFeeData?.length">
                    <template v-for="(item, key) in tab.serviceFeeData" :key="key">
                      <wd-cell-group v-if="Number(item.parentCashbackAmt) > 0" border custom-class="custom-rule-group">
                        <view class="gap-primary" />
                        <wd-cell center custom-class="rule-cell">
                          <template #title>
                            <wd-checkbox :model-value="item.configId">
                              {{ item.policyName }}
                            </wd-checkbox>
                          </template>
                          <template v-if="checkedServiceRules.includes(item.configId)">
                            <wd-input
                              v-model="item.cashbackAmt" type="text" placeholder="设置返现金额" use-suffix-slot
                              :prop="`policyConfigByChannelGroup.${cIndex}.channelData.${tabIndex}.serviceFeeData.${key}.cashbackAmt`"
                              :rules="serviceCashbackAmtRules(item.configId, item.parentCashbackAmt)"
                            >
                              <template #suffix>
                                <text>元</text>
                              </template>
                            </wd-input>
                          </template>
                        </wd-cell>
                        <template v-if="checkedServiceRules.includes(item.configId)">
                          <wd-cell :label="`注: 下级必须低于或等于${item.parentCashbackAmt}元`" custom-class="rule-desc" />
                        </template>
                      </wd-cell-group>
                    </template>
                  </template>
                  <wd-notice-bar v-else text="没有规则配置哦~" prefix="warn-bold" :scrollable="false" />

                  <view class="gap-primary" />
                  <wd-cell-group title="通讯费返现规则" border />
                  <template v-if="tab.simFeeData?.some((s:any) => s.data.length)">
                    <view v-for="(period, idx) in tab.simFeeData" :key="idx">
                      <template v-if="period.data?.some((p:any) => Number(p.parentCashbackAmt) > 0)">
                        <view class="gap-primary" />
                        <wd-cell :title="period.name" />
                        <wd-checkbox-group v-model="period.checkedList" custom-class="custom-checkbox-group">
                          <template v-for="(item, key) in period.data || []" :key="key">
                            <wd-cell-group v-if="Number(item.parentCashbackAmt) > 0" border custom-class="custom-rule-group">
                              <view class="h-6px bg-primary" />
                              <wd-cell center custom-class="rule-cell">
                                <template #title>
                                  <wd-checkbox :model-value="item.configId">
                                    {{ item.policyName }}
                                  </wd-checkbox>
                                </template>
                                <template v-if="period.checkedList.includes(item.configId)">
                                  <wd-input
                                    v-model="item.cashbackAmt" type="text" placeholder="设置返现金额" use-suffix-slot
                                    :prop="`policyConfigByChannelGroup.${cIndex}.channelData.${tabIndex}.simFeeData.${idx}.data.${key}.cashbackAmt`"
                                    :rules="simFeeCashbackAmtRules(item.configId, item.parentCashbackAmt, period)"
                                  >
                                    <template #suffix>
                                      <text>元</text>
                                    </template>
                                  </wd-input>
                                </template>
                              </wd-cell>
                              <template v-if="period.checkedList.includes(item.configId)">
                                <wd-cell :label="`注: 下级必须低于或等于${item.parentCashbackAmt}元`" custom-class="rule-desc" />
                              </template>
                            </wd-cell-group>
                          </template>
                        </wd-checkbox-group>
                      </template>
                    </view>
                  </template>
                  <wd-notice-bar v-else text="没有规则配置哦~" prefix="warn-bold" :scrollable="false" />
                </wd-tab>
              </wd-tabs>
            </wd-tab>
          </wd-tabs>
        </wd-checkbox-group>
      </wd-form>
    </view>

    <view class="shrink-0 p-30px">
      <wd-button
        type="primary" :loading="loading" block
        @click="save"
      >
        保存
      </wd-button>
    </view>

    <wd-toast />
  </view>
</template>

<script lang="ts" setup>
import type { FormInstance, FormItemRule, FormRules } from 'wot-design-uni/components/wd-form/types';
import { useToast } from 'wot-design-uni';
import { RatePolicyApi } from '@/api-org/rate-policy';
import { deepClone } from '@/utils';
import { CommonApi } from '@/api/common';

const simFeePolicyPeriodGroupDef: any = [
  {
    name: '第一期',
    field: 'firstPeriodList',
    data: [],
  },
  {
    name: '第二期',
    field: 'secondPeriodList',
    data: [],
  },
  {
    name: '第三期',
    field: 'thirdPeriodList',
    data: [],
  },
  {
    name: '标准期 (第四期以及后续阶段)',
    field: 'fourthPeriodList',
    data: [],
  },
];

const policyConfigByTerminalSourceDef: any = [
  {
    label: '全款机配置',
    key: 1,
    serviceFeeData: [],
    simFeeData: [],
  },
  {
    label: '分期机配置',
    key: 2,
    serviceFeeData: [],
    simFeeData: [],
  },
];

interface IPolicyConfigByChannelGroupItem {
  channelCode: string;
  channelName: string;
  channelData: any[];
}

const loading = ref(false);

const toast = useToast();

const checkedServiceRules = ref<any[]>([]);

// 表单
const formRef = ref<FormInstance | null>(null);

const form = reactive({
  id: '',
  activityCashbackPolicyName: '',
});

// 规则
const rules: FormRules = {
  activityCashbackPolicyName: [{ required: true, message: '请输入模板名称' }],
};

const ruleFormRef = ref<FormInstance | null>(null);

const ruleForm = reactive<any>({
  serviceFeePolicyDTOList: [],
  simFeePolicyDTOList: [],
  policyConfigByChannelGroup: [],
});

const channelCodes = ref<any>([]);

const activeChannelTabKey = ref('0');
const activeTerminalSourceTabKey = ref('1');

onLoad(async (query) => {
  await getChannelList();

  if (query?.id) {
    form.id = query.id;
    getDetail();
  }
  else {
    queryAddDisplay();
  }
});

onReady(() => {
  uni.setNavigationBarTitle({
    title: form.id ? '活动返现模板编辑' : '活动返现模板新增',
  });
});

async function save() {
  // 检验表单
  const { valid, errors } = await formRef.value!.validate();
  if (!valid)
    return Promise.reject(errors);

  // 检验规则表单
  const { valid: ruleValid, errors: ruleErrors } = await ruleFormRef.value!.validate();
  if (!ruleValid)
    return Promise.reject(ruleErrors);

  // if (checkedServiceRules.value.length === 0) {
  //   toast.warning('请至少选择一个服务费返现规则');
  //   return Promise.reject();
  // }
  // if (checkedSimRules.value.length === 0) {
  //   toast.warning('');
  //   return Promise.reject();
  // }

  loading.value = true;

  let serviceFeePolicyDTOList: any = [];
  const simPolicySubmitData: any = {};

  ruleForm.policyConfigByChannelGroup.forEach((channel: any) => {
    channel.channelData.forEach((item: any) => {
      // 格式化服务费
      const checkedServiceFeeData = item.serviceFeeData.filter((i: any) => checkedServiceRules.value.includes(i.configId));
      serviceFeePolicyDTOList = [...serviceFeePolicyDTOList, ...checkedServiceFeeData];

      // 格式化流量费
      item.simFeeData.forEach((period: any) => {
        const checkedSimFeeData = period.data.filter((i: any) => period.checkedList.includes(i.configId));
        simPolicySubmitData[period.field] = [...(simPolicySubmitData[period.field] || []), ...checkedSimFeeData];
      });
    });
  });

  const depositPolicy = serviceFeePolicyDTOList;
  const simFeeNewPolicyDTO = simPolicySubmitData;

  const params: any = {
    activityCashbackPolicyName: form.activityCashbackPolicyName,
    depositPolicy,
    simFeeNewPolicyDTO,
  };

  let result = null;
  if (form.id) {
    // 编辑
    params.id = form.id;
    result = RatePolicyApi.editActivityCashbackTemplate(params);
  }
  else {
    result = RatePolicyApi.addActivityCashbackTemplate(params);
  }
  // 提交
  result
    .then(() => {
      toast.success({
        msg: '操作成功',
        closed: () => {
          uni.navigateBack();
        },
      });
    })
    .finally(() => {
      loading.value = false;
    });
}

async function getChannelList() {
  const data = await CommonApi.getChannelList();
  channelCodes.value = data || [];
}

async function getDetail() {
  const data = await RatePolicyApi.detailActivityCashbackTemplate({
    id: form.id,
  });

  form.activityCashbackPolicyName = data.activityCashbackPolicyName;

  ruleForm.serviceFeePolicyDTOList = data.serviceFeePolicyDTOList || [];
  ruleForm.serviceFeePolicyDTOList.forEach((i: any) => {
    if (i.openStatus) {
      checkedServiceRules.value.push(i.configId);
    }
    else if (!(Number(i.parentCashbackAmt) > 0)) {
      checkedServiceRules.value.push(i.configId);
      i.cashbackAmt = 0;
    }
    else {
      i.cashbackAmt = '';
    }
  });

  const simFeePolicyMap = data.simFeeNewPolicyDTO || {};
  const simFeePolicyPeriodGroupMap = deepClone(simFeePolicyPeriodGroupDef);
  simFeePolicyPeriodGroupMap.forEach((period: any) => {
    Object.keys(simFeePolicyMap).forEach((key) => {
      if (period.field === key) {
        period.data = simFeePolicyMap[key];
        period.checkedList = [];
        period.data.forEach((item: any) => {
          if (item.openStatus) {
            period.checkedList.push(item.configId);
          }
          else if (!(Number(item.parentCashbackAmt) > 0)) {
            period.checkedList.push(item.configId);
            item.cashbackAmt = 0;
          }
          else {
            item.cashbackAmt = '';
          }
        });
      }
    });
  });

  // 找出所有通道
  let allChannelCodes: any = [];
  try {
    const channelCodes = new Set(
      [...(ruleForm.serviceFeePolicyDTOList || []), ...Object.values(simFeePolicyMap || {})]
        .flat(Infinity)
        .flatMap(item => (Array.isArray(item) ? item.map(subItem => subItem?.channelCode) : item?.channelCode))
        .filter(code => code?.trim())
        .map(code => code.trim()),
    );
    allChannelCodes = [...channelCodes];
  }
  catch (error) {
    console.log(error);
  }

  const policyConfigByChannelGroup: IPolicyConfigByChannelGroupItem[] = [];
  allChannelCodes.forEach((item: any) => {
    const channelItem = channelCodes.value.find((channel: any) => channel.channelCode === item);
    policyConfigByChannelGroup.push({
      channelCode: item,
      channelName: channelItem?.channelName,
      channelData: deepClone(policyConfigByTerminalSourceDef),
    });
  });

  policyConfigByChannelGroup.forEach((channel: IPolicyConfigByChannelGroupItem) => {
    channel.channelData.forEach((tab) => {
      tab.serviceFeeData = ruleForm.serviceFeePolicyDTOList.filter((item: any) => item.channelCode === channel.channelCode && item.terminalSource === tab.key);

      const simFeeData = simFeePolicyPeriodGroupMap.map((period: any) => {
        return {
          ...period,
          data: period.data.filter((item: any) => item.channelCode === channel.channelCode && item.terminalSource === tab.key),
        };
      });
      tab.simFeeData = simFeeData;
    });
  });

  ruleForm.policyConfigByChannelGroup = policyConfigByChannelGroup;
}

async function queryAddDisplay() {
  const data = await RatePolicyApi.queryAddDisplay();

  const serviceFeePolicyDTOList = data.serviceFeePolicyDTOList || [];
  serviceFeePolicyDTOList.forEach((i: any) => {
    if (!(Number(i.parentCashbackAmt) > 0)) {
      i.cashbackAmt = 0;
      checkedServiceRules.value.push(i.configId);
    }
  });
  ruleForm.serviceFeePolicyDTOList = serviceFeePolicyDTOList;

  const simFeePolicyMap = data.simFeeNewPolicyDTO || {};
  const simFeePolicyPeriodGroupMap = deepClone(simFeePolicyPeriodGroupDef);
  simFeePolicyPeriodGroupMap.forEach((period: any) => {
    Object.keys(simFeePolicyMap).forEach((key) => {
      if (period.field === key) {
        period.data = simFeePolicyMap[key] || [];
        period.checkedList = [];
        period.data.forEach((item: any) => {
          if (!(Number(item.parentCashbackAmt) > 0)) {
            item.cashbackAmt = 0;
            period.checkedList.push(item.configId);
          }
        });
      }
    });
  });

  // 找出所有通道
  let allChannelCodes: any = [];
  try {
    const channelCodes = new Set(
      [...(ruleForm.serviceFeePolicyDTOList || []), ...Object.values(simFeePolicyMap || {})]
        .flat(Infinity)
        .flatMap(item => (Array.isArray(item) ? item.map(subItem => subItem?.channelCode) : item?.channelCode))
        .filter(code => code?.trim())
        .map(code => code.trim()),
    );
    allChannelCodes = [...channelCodes];
  }
  catch (error) {
    console.log(error);
  }

  const policyConfigByChannelGroup: IPolicyConfigByChannelGroupItem[] = [];
  allChannelCodes.forEach((item: any) => {
    const channelItem = channelCodes.value.find((channel: any) => channel.channelCode === item);
    policyConfigByChannelGroup.push({
      channelCode: item,
      channelName: channelItem?.channelName,
      channelData: deepClone(policyConfigByTerminalSourceDef),
    });
  });

  policyConfigByChannelGroup.forEach((channel: IPolicyConfigByChannelGroupItem) => {
    channel.channelData.forEach((tab) => {
      tab.serviceFeeData = ruleForm.serviceFeePolicyDTOList.filter((item: any) => item.channelCode === channel.channelCode && item.terminalSource === tab.key);

      const simFeeData = simFeePolicyPeriodGroupMap.map((period: any) => {
        return {
          ...period,
          data: period.data.filter((item: any) => item.channelCode === channel.channelCode && item.terminalSource === tab.key),
        };
      });
      tab.simFeeData = simFeeData;
    });
  });

  ruleForm.policyConfigByChannelGroup = policyConfigByChannelGroup;
}

function simFeeCashbackAmtRules(key: any, maxCashbackAmt: any, period: any): FormItemRule[] {
  if (period.checkedList.includes(key)) {
    return [
      {
        required: true,
        message: '请输入',
        validator: async (value) => {
          if (Number(value) > Number(maxCashbackAmt)) {
            return Promise.reject(`注：下级必须低于或等于${maxCashbackAmt}元`);
          }
          return Promise.resolve();
        },
        trigger: 'blur',
      },
    ];
  }
  return [];
}

function serviceCashbackAmtRules(key: any, maxCashbackAmt: any): FormItemRule[] {
  if (checkedServiceRules.value.includes(key)) {
    return [
      {
        required: true,
        message: '请输入',
        validator: async (value) => {
          if (Number(value) > Number(maxCashbackAmt)) {
            return Promise.reject(`注：下级必须低于或等于${maxCashbackAmt}元`);
          }
          return Promise.resolve();
        },
        trigger: 'blur',
      },
    ];
  }
  return [];
}
</script>

<style lang="scss" scoped>
:deep(.custom-checkbox-group){
 background-color: transparent;

 .wd-checkbox{
   margin-bottom: 0;
  }
}

:deep(.custom-rule-group){
  .wd-cell__wrapper{
     justify-content: flex-start;
    }

  .rule-cell{
   .wd-cell__left{
    flex: none;
    flex-shrink: 0;
    max-width: 35%;
   }

   .wd-cell__right{
    flex: none;
   }

   .wd-input__inner{
    height: 30px;
   }

    .wd-input__body{
      width: 240rpx;
      text-align: center;
    }
  }

  .rule-desc{
    .wd-cell__label{
      margin-top: 0;
    }

    .wd-cell__right{
      flex: none;
    }
  }
}
</style>
