<template>
  <view class="h-full flex flex-col">
    <view class="shrink-0">
      <view class="gap-primary" />
      <wd-form ref="formRef" :model="form" :rules="rules">
        <wd-cell-group border>
          <wd-select-picker v-model="form.policyType" label="政策类型" label-width="80px" :columns="policyTypeOptions" type="radio" readonly />
          <wd-input v-model="form.policyDesc" label="政策名称" placeholder="请输入结算政策名称" prop="policyDesc" label-width="80px" />
          <!-- 渠道复选框 -->
          <wd-cell title="开通渠道" title-width="80px" center>
            <wd-checkbox-group v-model="checkedChannels" inline custom-class="flex items-center flex-wrap">
              <wd-checkbox v-for="(item, key) in channelOptions" :key="key" :model-value="item.channelCode" :disabled="item.disabled">
                {{ item.channelName }}
              </wd-checkbox>
            </wd-checkbox-group>
          </wd-cell>
        </wd-cell-group>
      </wd-form>
    </view>

    <view class="grow overflow-hidden">
      <wd-tabs v-model="currentChannelTab" slidable="always" custom-class="custom-rate-tabs">
        <wd-form ref="rateFormRef" :model="rateForm">
          <template v-for="(item, key) in rateForm.rateMapDTOList" :key="key">
            <wd-tab v-if="checkedChannels.includes(item.channelCode)" :title="item.channelName" :name="item.channelCode">
              <template v-for="(rateItem, keyr) in item.rateDTOList" :key="keyr">
                <RateModule
                  :rate-item="rateItem"
                  :form-prop-prefix="['rateMapDTOList', key, 'rateDTOList', keyr]"
                  :show-d0-single-fee="showD0SingleFee"
                />
              </template>
            </wd-tab>
          </template>
        </wd-form>
      </wd-tabs>
    </view>

    <view class="shrink-0 p-30px">
      <wd-button
        type="primary" :loading="loading" block
        @click="save"
      >
        保存
      </wd-button>
    </view>

    <wd-toast />
    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import type { FormInstance, FormRules } from 'wot-design-uni/components/wd-form/types';
import { useMessage, useToast } from 'wot-design-uni';
import { RatePolicyApi } from '@/api-org/rate-policy';
import { CommonApi } from '@/api/common';
import { buildUrlWithParams, decodeUrlParams } from '@/utils';

const message = useMessage();

const policyTypeOptions = [
  {
    label: '下级团队结算政策',
    value: 1,
  },
  {
    label: '商户结算政策',
    value: 2,
  },
];

const showD0SingleFee = ref<boolean>(false);

const currentChannelTab = ref<undefined | string>(undefined);
type ChannelItem = {
  channelCode: string;
  channelName: string;
  disabled?: boolean;
};
const channelOptions = ref<ChannelItem[]>([]);

const checkedChannels = ref<string[]>([]);

const loading = ref(false);

const toast = useToast();

const isUpate = ref<boolean>(false);

const formRef = ref<FormInstance | null>(null);

const form = reactive<any>({
  id: '',
  policyDesc: '',
  policyType: '',
});

// 规则
const rules: FormRules = {
  policyDesc: [{ required: true, message: '请输入政策名称' }],
};

const rateFormRef = ref<FormInstance | null>(null);

const rateForm = reactive<any>({
  rateMapDTOList: [],
});

onLoad((query) => {
  query = decodeUrlParams(query || {});

  if (query.id) {
    isUpate.value = true;
    form.id = query.id;
  }
  if (query.policyDesc) {
    form.policyDesc = query.policyDesc;
  }
  if (query.policyType) {
    form.policyType = Number(query.policyType);
    if (form.policyType === 2) {
      showD0SingleFee.value = true;
    }
  }

  init();
});

onReady(() => {
  uni.setNavigationBarTitle({
    title: isUpate.value ? '编辑费率政策' : '新增费率政策',
  });
});

async function save() {
  // 校验表单
  const { valid, errors } = await formRef.value!.validate();
  if (!valid)
    return Promise.reject(errors);

  // 检验费率表单
  const { valid: rateValid, errors: rateErrors } = await rateFormRef.value!.validate();
  if (!rateValid)
    return Promise.reject(rateErrors);

  // 校验是否有选中渠道
  if (checkedChannels.value.length === 0) {
    toast.warning('请至少选择一个渠道');
    return Promise.reject();
  }

  const rateDTOList: any = [];
  checkedChannels.value.forEach((channelCode) => {
    const rateMapDTOItem = rateForm.rateMapDTOList.find((item: any) => item.channelCode === channelCode);
    if (rateMapDTOItem) {
      rateDTOList.push(...rateMapDTOItem.rateDTOList);
    }
  });

  const params: any = {
    policyType: form.policyType,
    policyDesc: form.policyDesc,
    rateDTOList,
  };

  loading.value = true;

  let result = null;

  if (isUpate.value) {
    params.id = form.id;
    result = RatePolicyApi.editPolicy(params);
  }
  else {
    result = RatePolicyApi.addPolicy(params);
  }

  // 提交
  result
    .then(() => {
      if (isUpate.value) {
        if (form.policyType === 2) {
          message
            .confirm({
              msg: '费率政策修改成功, 需要进行商户费率政策同步才能生效, 是否立即同步商户费率政策?',
              title: '提示',
              confirmButtonText: '同步政策',
            })
            .then(async () => {
              await RatePolicyApi.syncMerchantRatePolicy({
                id: form.id,
              });
              toast.success({
                msg: '操作成功',
                closed: () => {
                  uni.navigateBack();
                },
              });
            })
            .catch(() => {
              uni.navigateBack();
            });
        }
        else {
          message
            .confirm({
              msg: '费率政策修改成功, 需要进行政策同步才能生效, 是否立即前往同步政策?',
              title: '提示',
              confirmButtonText: '同步政策',
            })
            .then(() => {
              const url = buildUrlWithParams('/pages-org/rate-policy/rate-policy-sync', {
                id: form.id,
                policyDesc: form.policyDesc,

              });
              uni.redirectTo({ url });
            })
            .catch(() => {
              uni.navigateBack();
            });
        }
      }
      else {
        toast.success({
          msg: '操作成功',
          closed: () => {
            uni.navigateBack();
          },
        });
      }
    })
    .finally(() => {
      loading.value = false;
    });
}

async function init() {
  const channels = await CommonApi.getChannelList({});
  const data = await RatePolicyApi.getOwnerRateList();

  const { selfRateMap } = data || {};

  const selfChannels = Object.keys(selfRateMap || {});

  let rateListMap = null;

  if (isUpate.value) {
    const detailData = await RatePolicyApi.detailItemPolicy({
      id: form.id,
    });
    const { rateMap } = detailData || {};

    const setedChannel = Object.keys(rateMap || {});

    checkedChannels.value = setedChannel;

    setedChannel.forEach((code) => {
      if (selfChannels.includes(code)) {
        const setedTemplateNo = rateMap[code].map((r: any) => r.templateNo);
        const allTemplateNo = selfRateMap[code].map((a: any) => a.templateNo);
        const addedTemplateNo = allTemplateNo.filter((key: string) => !setedTemplateNo.includes(key));

        const addedTemplate = selfRateMap[code].filter((i: any) => addedTemplateNo.includes(i.templateNo));
        addedTemplate.forEach((t: any) => {
          t.isAddedTemp = true;
        });

        rateMap[code].push(...addedTemplate);
      }
    });

    rateListMap = rateMap;
  }
  else {
    rateListMap = selfRateMap;
  }

  const rateList = Object.values(rateListMap || {}).flat();
  rateList.forEach((item: any) => {
    item.rateInfoDTO = item.rateInfoDTO || {};
    item.isSame = 1;
  });

  rateForm.rateMapDTOList = [];

  selfChannels.forEach((channelCode) => {
    const channel = channels.find((item: ChannelItem) => item.channelCode === channelCode);
    if (channel) {
      rateForm.rateMapDTOList.push({
        channelCode,
        channelName: channel.channelName,
        rateDTOList: rateListMap[channelCode],
      });
      channelOptions.value.push(channel);
    }
  });
}
</script>

<style lang="scss" scoped>
:deep(.custom-rate-tabs){
  height: 100%;

  .wd-tabs__container{
    overflow-y: scroll;
    height: calc(100% - 42px);
  }
}
</style>
