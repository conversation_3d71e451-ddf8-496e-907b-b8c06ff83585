<template>
  <view class="h-full bg-primary">
    <page-paging ref="pagingRef" v-model="datasource" @query="queryDatasource">
      <!-- 主体 -->
      <view class="p-20rpx">
        <view
          v-for="(item, key) in datasource" :key="key"
          class="mb-30rpx border border-#edf0f3 rounded-lg border-solid bg-white"
        >
          <view class="p-8px">
            <wd-cell-group custom-class="cell-group">
              <wd-cell title="拓展码名称" :value="item.extensionName" />
              <wd-cell title="创建时间" :value="item.createTime" />
            </wd-cell-group>
          </view>

          <view class="flex items-center justify-end border border-#f4f4f4 border-t-solid p-20rpx">
            <wd-tag round @click="handleEdit(item.id)">
              编辑拓展码
            </wd-tag>
            <wd-tag custom-class="ml-8px" round type="primary" @click="handleToGenerateQRcode(item)">
              生成拓展码
            </wd-tag>
          </view>
        </view>
      </view>

      <!-- 底部 -->
      <template #bottom>
        <view class="p-30rpx">
          <wd-button block size="large" type="primary" @click="handleAdd">
            新增
          </wd-button>
        </view>
      </template>
    </page-paging>

    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import { ExtensionCodeApi } from '@/api-org/extension-code';
import { buildUrlWithParams } from '@/utils';

const pagingRef = ref();

const datasource = ref<any[]>([]);

onShow(() => {
  pagingRef.value?.reload();
});

function queryDatasource(pageNo: number, pageSize: number) {
  ExtensionCodeApi.findPage({ pageNo, pageSize })
    .then((res) => {
      pagingRef.value.completeByTotal(res?.rows, res?.totalRows);
    })
    .catch(() => {
      pagingRef.value.completeByTotal(false);
    });
}

function handleAdd() {
  uni.navigateTo({ url: '/pages-org/extension-code/extension-code-update' });
}

function handleEdit(id: number) {
  uni.navigateTo({ url: `/pages-org/extension-code/extension-code-update?id=${id}` });
}

function handleToGenerateQRcode(item: any) {
  const url = buildUrlWithParams('/pages-org/extension-code/extension-code-qrcode', {
    id: item.id,
    extensionName: item.extensionName,
  });
  uni.navigateTo({ url });
}
</script>

<style lang="scss" scoped>
:deep(.cell-group) {
  .wd-cell {
    @apply p-0;
  }

  .wd-cell__wrapper {
    @apply p-2px;
  }
}
</style>
