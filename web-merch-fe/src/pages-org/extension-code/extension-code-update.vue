<template>
  <view class="h-full flex flex-col overflow-hidden bg-primary">
    <view class="my-10px shrink-0">
      <wd-form ref="formRef" :model="form" :rules="rules" custom-class="error-message__align-right">
        <view>
          <wd-input v-model="form.extensionName" label="拓展码名称" placeholder="请输入拓展码名称" prop="extensionName" label-width="100px" clearable align-right />
        </view>

        <view class="mt-10px">
          <wd-tabs v-model="currentTab" @change="onChangeTab">
            <view class="gap-primary" />
            <wd-tab title="结算政策" :name="0">
              <wd-cell
                title="交易结算政策" title-width="100px"
                prop="ratePolicyId" is-link
                @click="onSelectRatePolicy"
              >
                <wd-textarea
                  v-model="form.ratePolicyName"
                  placeholder="请选择"
                  auto-height no-border readonly
                  custom-textarea-class="text-right"
                />
              </wd-cell>
            </wd-tab>
            <wd-tab title="返现政策" :name="1">
              <wd-cell
                title="活动返现政策" title-width="100px"
                is-link
                @click="onSelectCashbackPolicy"
              >
                <wd-textarea
                  v-model="form.cashbackPolicyName"
                  placeholder="请选择"
                  auto-height no-border readonly
                  custom-textarea-class="text-right"
                />
              </wd-cell>
            </wd-tab>
          </wd-tabs>
        </view>
      </wd-form>
    </view>

    <view class="grow overflow-y-scroll">
      <!-- 费率政策 -->
      <view v-show="currentTab === 0">
        <wd-tabs v-model="currentChannelTab" slidable="always" custom-class="custom-rate-tabs">
          <template v-for="(item, key) in form.rateMapDTOList" :key="key">
            <wd-tab :title="item.channelName">
              <template v-for="(rateItem, keyr) in item.rateDTOList" :key="keyr">
                <RateModule
                  :rate-item="rateItem"
                  :readonly="true"
                />
              </template>
            </wd-tab>
          </template>
        </wd-tabs>
      </view>

      <!-- 返现规则 -->
      <view v-show="currentTab === 1">
        <wd-form ref="ruleFormRef" :model="ruleForm">
          <wd-checkbox-group v-model="checkedServiceRules" custom-class="custom-checkbox-group">
            <wd-tabs v-model="activeChannelTabKey" custom-class="custom-rate-tabs" slidable="always">
              <wd-tab v-for="(channel, cIndex) in ruleForm.policyConfigByChannelGroup || []" :key="cIndex" :title="channel.channelName" :name="String(cIndex)" :lazy="false">
                <wd-tabs v-model="activeTerminalSourceTabKey" custom-class="custom-rate-tabs">
                  <wd-tab v-for="(tab, tabIndex) in channel.channelData || []" :key="tabIndex" :title="tab.label" :name="String(tab.key)" :lazy="false">
                    <view class="gap-primary" />
                    <wd-cell-group title="服务费返现规则" border />
                    <template v-if="tab.serviceFeeData?.length">
                      <template v-for="(item, key) in tab.serviceFeeData" :key="key">
                        <wd-cell-group v-if="Number(item.parentCashbackAmt) > 0" border custom-class="custom-rule-group">
                          <view class="gap-primary" />
                          <wd-cell center custom-class="rule-cell">
                            <template #title>
                              <wd-checkbox :model-value="item.configId">
                                {{ item.policyName }}
                              </wd-checkbox>
                            </template>
                            <template v-if="checkedServiceRules.includes(item.configId)">
                              <wd-input
                                v-model="item.cashbackAmt" type="text" placeholder="设置返现金额" use-suffix-slot
                                :prop="`policyConfigByChannelGroup.${cIndex}.channelData.${tabIndex}.serviceFeeData.${key}.cashbackAmt`"
                                :rules="serviceCashbackAmtRules(item.configId, item.parentCashbackAmt)"
                              >
                                <template #suffix>
                                  <text>元</text>
                                </template>
                              </wd-input>
                            </template>
                          </wd-cell>
                          <template v-if="checkedServiceRules.includes(item.configId)">
                            <wd-cell :label="`注: 下级必须低于或等于${item.parentCashbackAmt}元`" custom-class="rule-desc" />
                          </template>
                        </wd-cell-group>
                      </template>
                    </template>
                    <wd-notice-bar v-else text="没有规则配置哦~" prefix="warn-bold" :scrollable="false" />

                    <view class="gap-primary" />
                    <wd-cell-group title="通讯费返现规则" border />
                    <template v-if="tab.simFeeData?.some((s:any) => s.data.length)">
                      <view v-for="(period, idx) in tab.simFeeData" :key="idx">
                        <template v-if="period.data?.some((p:any) => Number(p.parentCashbackAmt) > 0)">
                          <view class="gap-primary" />
                          <wd-cell :title="period.name" />
                          <wd-checkbox-group v-model="period.checkedList" custom-class="custom-checkbox-group">
                            <template v-for="(item, key) in period.data || []" :key="key">
                              <wd-cell-group v-if="Number(item.parentCashbackAmt) > 0" border custom-class="custom-rule-group">
                                <view class="h-6px bg-primary" />
                                <wd-cell center custom-class="rule-cell">
                                  <template #title>
                                    <wd-checkbox :model-value="item.configId">
                                      {{ item.policyName }}
                                    </wd-checkbox>
                                  </template>
                                  <template v-if="period.checkedList.includes(item.configId)">
                                    <wd-input
                                      v-model="item.cashbackAmt" type="text" placeholder="设置返现金额" use-suffix-slot
                                      :prop="`policyConfigByChannelGroup.${cIndex}.channelData.${tabIndex}.simFeeData.${idx}.data.${key}.cashbackAmt`"
                                      :rules="simFeeCashbackAmtRules(item.configId, item.parentCashbackAmt, period)"
                                    >
                                      <template #suffix>
                                        <text>元</text>
                                      </template>
                                    </wd-input>
                                  </template>
                                </wd-cell>
                                <template v-if="period.checkedList.includes(item.configId)">
                                  <wd-cell :label="`注: 下级必须低于或等于${item.parentCashbackAmt}元`" custom-class="rule-desc" />
                                </template>
                              </wd-cell-group>
                            </template>
                          </wd-checkbox-group>
                        </template>
                      </view>
                    </template>
                    <wd-notice-bar v-else text="没有规则配置哦~" prefix="warn-bold" :scrollable="false" />
                  </wd-tab>
                </wd-tabs>
              </wd-tab>
            </wd-tabs>
          </wd-checkbox-group>
        </wd-form>
      </view>
    </view>

    <view class="shrink-0 p-30px">
      <wd-button
        type="primary" :loading="loading" block
        @click="save"
      >
        保存
      </wd-button>
    </view>

    <wd-toast />
  </view>
</template>

<script lang="ts" setup>
import type { FormInstance, FormItemRule, FormRules } from 'wot-design-uni/components/wd-form/types';
import { useToast } from 'wot-design-uni';
import { RatePolicyApi } from '@/api-org/rate-policy';
import { emitter } from '@/utils/emitter';
import { ExtensionCodeApi } from '@/api-org/extension-code';
import { CommonApi } from '@/api/common';
import { deepClone } from '@/utils';

const simFeePolicyPeriodGroupDef: any = [
  {
    name: '第一期',
    field: 'firstPeriodList',
    data: [],
  },
  {
    name: '第二期',
    field: 'secondPeriodList',
    data: [],
  },
  {
    name: '第三期',
    field: 'thirdPeriodList',
    data: [],
  },
  {
    name: '标准期 (第四期以及后续阶段)',
    field: 'fourthPeriodList',
    data: [],
  },
];

const policyConfigByTerminalSourceDef: any = [
  {
    label: '全款机配置',
    key: 1,
    serviceFeeData: [],
    simFeeData: [],
  },
  {
    label: '分期机配置',
    key: 2,
    serviceFeeData: [],
    simFeeData: [],
  },
];

interface IPolicyConfigByChannelGroupItem {
  channelCode: string;
  channelName: string;
  channelData: any[];
}

const activeChannelTabKey = ref('0');
const activeTerminalSourceTabKey = ref('1');

const toast = useToast();

const isUpdate = ref(false);

const currentTab = ref(0);
const currentChannelTab = ref(0);

const loading = ref(false);

// 表单
const formRef = ref<FormInstance | null>(null);

const form = reactive<any>({
  id: '',
  extensionName: '',
  rateMapDTOList: [],
});

// 规则
const rules: FormRules = {
  extensionName: [{ required: true, message: '请输入拓展码名称' }],
  ratePolicyId: [{ required: true, message: '请选择' }],
};

const checkedServiceRules = ref<any[]>([]);

const ruleFormRef = ref<FormInstance | null>(null);

const ruleForm = reactive<any>({
  serviceFeePolicyDTOList: [],
  simFeePolicyDTOList: [],
  policyConfigByChannelGroup: [],
});

const channelCodes = ref<any>([]);

const oriPolicyDTOListMap: any = {
  serviceFeePolicyDTOList: [],
  simFeeNewPolicyDTO: {},
};

onLoad(async (query) => {
  await getChannelList();

  if (query?.id) {
    form.id = query.id;
    isUpdate.value = true;
    await queryAddDisplay();
    getDetail();
  }
});

onReady(() => {
  uni.setNavigationBarTitle({
    title: isUpdate.value ? '编辑拓展码' : '新增拓展码',
  });
});

async function save() {
  // 检验表单
  const { valid, errors } = await formRef.value!.validate();
  if (!valid) {
    if (form.extensionName) {
      toast.warning('结算政策信息有误');
    }
    return Promise.reject(errors);
  }

  // 检验规则表单
  const { valid: ruleValid, errors: ruleErrors } = await ruleFormRef.value!.validate();
  if (!ruleValid) {
    toast.warning('返现政策信息有误');
    return Promise.reject(ruleErrors);
  }

  // if (checkedServiceRules.value.length === 0) {
  //   toast.warning('请至少选择一个服务费返现规则');
  //   return Promise.reject();
  // }
  // if (checkedSimRules.value.length === 0) {
  //   toast.warning('请至少选择一个通讯费返现规则');
  //   return Promise.reject();
  // }

  loading.value = true;

  let serviceFeePolicyDTOList: any = [];
  const simPolicySubmitData: any = {};

  ruleForm.policyConfigByChannelGroup.forEach((channel: any) => {
    channel.channelData.forEach((item: any) => {
      // 格式化服务费
      const checkedServiceFeeData = item.serviceFeeData.filter((i: any) => checkedServiceRules.value.includes(i.configId));
      serviceFeePolicyDTOList = [...serviceFeePolicyDTOList, ...checkedServiceFeeData];

      // 格式化流量费
      item.simFeeData.forEach((period: any) => {
        const checkedSimFeeData = period.data.filter((i: any) => period.checkedList.includes(i.configId));
        simPolicySubmitData[period.field] = [...(simPolicySubmitData[period.field] || []), ...checkedSimFeeData];
      });
    });
  });

  const params: any = {
    extensionName: form.extensionName,
    ratePolicyId: form.ratePolicyId,
    serviceFeePolicyDTOList,
    simFeeNewPolicyDTO: simPolicySubmitData,
  };

  let result = null;
  if (isUpdate.value) {
    // 编辑
    params.id = form.id;
    result = ExtensionCodeApi.edit(params);
  }
  else {
    result = ExtensionCodeApi.add(params);
  }
  // 提交
  result
    .then(() => {
      toast.success({
        msg: '操作成功',
        closed: () => {
          uni.navigateBack();
        },
      });
    })
    .finally(() => {
      loading.value = false;
    });
}

async function getDetail() {
  const data = await ExtensionCodeApi.detail({
    id: form.id,
  });

  form.ratePolicyId = data.ratePolicyId;
  form.ratePolicyName = data.ratePolicyName;
  form.extensionName = data.extensionName;

  const serviceFeePolicyDTOMap: any = {};
  const simFeePolicyDTOMap: any = {};
  // 自身全部服务费
  oriPolicyDTOListMap.serviceFeePolicyDTOList.forEach((s: any) => {
    serviceFeePolicyDTOMap[s.configId] = s;
  });

  // 自身全部流量费
  Object.keys(oriPolicyDTOListMap.simFeeNewPolicyDTO || {}).forEach((key) => {
    simFeePolicyDTOMap[key] = Object.assign({}, simFeePolicyDTOMap[key] || {});
    oriPolicyDTOListMap.simFeeNewPolicyDTO[key].forEach((s: any) => {
      simFeePolicyDTOMap[key][s.configId] = s;
    });
  });

  ruleForm.serviceFeePolicyDTOList = data.serviceFeePolicyDTOList || [];
  ruleForm.serviceFeePolicyDTOList.forEach((i: any) => {
    const configItem = serviceFeePolicyDTOMap[i.configId] || {};
    i.parentCashbackAmt = configItem.parentCashbackAmt;

    if (i.openStatus) {
      checkedServiceRules.value.push(i.configId);
    }
    else if (!(Number(i.parentCashbackAmt) > 0)) {
      checkedServiceRules.value.push(i.configId);
      i.cashbackAmt = 0;
    }
    else {
      i.cashbackAmt = '';
    }
  });

  const simFeePolicyMap = data.simFeeNewPolicyDTO || {};
  const simFeePolicyPeriodGroupMap = deepClone(simFeePolicyPeriodGroupDef);
  simFeePolicyPeriodGroupMap.forEach((period: any) => {
    Object.keys(simFeePolicyMap).forEach((key) => {
      if (period.field === key) {
        period.data = simFeePolicyMap[key] || [];
        period.checkedList = [];
        period.data.forEach((item: any) => {
          const configItem = (simFeePolicyDTOMap[key] && simFeePolicyDTOMap[key][item.configId]) || {};
          item.parentCashbackAmt = configItem.parentCashbackAmt;

          if (item.openStatus) {
            period.checkedList.push(item.configId);
          }
          else if (!(Number(item.parentCashbackAmt) > 0)) {
            period.checkedList.push(item.configId);
            item.cashbackAmt = 0;
          }
          else {
            item.cashbackAmt = '';
          }
        });
      }
    });
  });

  // 找出所有通道
  let allChannelCodes: any = [];
  try {
    const channelCodes = new Set(
      [...(ruleForm.serviceFeePolicyDTOList || []), ...Object.values(simFeePolicyMap || {})]
        .flat(Infinity)
        .flatMap(item => (Array.isArray(item) ? item.map(subItem => subItem?.channelCode) : item?.channelCode))
        .filter(code => code?.trim())
        .map(code => code.trim()),
    );
    allChannelCodes = [...channelCodes];
  }
  catch (error) {
    console.log(error);
  }

  const policyConfigByChannelGroup: IPolicyConfigByChannelGroupItem[] = [];
  allChannelCodes.forEach((item: any) => {
    const channelItem = channelCodes.value.find((channel: any) => channel.channelCode === item);
    policyConfigByChannelGroup.push({
      channelCode: item,
      channelName: channelItem?.channelName,
      channelData: deepClone(policyConfigByTerminalSourceDef),
    });
  });

  policyConfigByChannelGroup.forEach((channel: IPolicyConfigByChannelGroupItem) => {
    channel.channelData.forEach((tab) => {
      tab.serviceFeeData = ruleForm.serviceFeePolicyDTOList.filter((item: any) => item.channelCode === channel.channelCode && item.terminalSource === tab.key);

      const simFeeData = simFeePolicyPeriodGroupMap.map((period: any) => {
        return {
          ...period,
          data: period.data.filter((item: any) => item.channelCode === channel.channelCode && item.terminalSource === tab.key),
        };
      });
      tab.simFeeData = simFeeData;
    });
  });

  ruleForm.policyConfigByChannelGroup = policyConfigByChannelGroup;

  // 费率返现
  const channels: any = channelCodes.value;

  const rateMap = data.rateMap || {};
  const setedChannel = Object.keys(rateMap);

  form.rateMapDTOList = [];

  setedChannel.forEach((channelCode) => {
    const channel = channels.find((item: any) => item.channelCode === channelCode);
    if (channel) {
      form.rateMapDTOList.push({
        channelCode,
        channelName: channel.channelName,
        rateDTOList: rateMap[channelCode],
      });
    }
  });
}

function onChangeTab({ index }: { index: number }) {
  if (index === 1) {
    queryAddDisplay();
  }
}

function onSelectRatePolicy() {
  uni.navigateTo({
    url: '/pages-org/picker-view/rate-policy/index',
  });
  emitter.on('picker-rate-policy', (data: any) => {
    form.ratePolicyId = data.id;
    form.ratePolicyName = data.policyDesc;
    form.rateMapDTOList = data.rateMapDTOList;
  });
}

function onSelectCashbackPolicy() {
  uni.navigateTo({
    url: '/pages-org/picker-view/cashback-policy/index',
  });
  emitter.on('picker-cashback-policy', (data: any) => {
    form.cashbackPolicyName = data.templateName;

    // 选中的
    const { serviceFeePolicyDTOList, simFeeNewPolicyDTO } = data || {};
    // 全部的
    const { serviceFeePolicyDTOList: oriServiceFeePolicyDTOList, simFeeNewPolicyDTO: oriSimFeeNewPolicyDTO } = deepClone(oriPolicyDTOListMap);

    // 设置服务费
    checkedServiceRules.value = [];
    if (serviceFeePolicyDTOList.length) {
      oriServiceFeePolicyDTOList.forEach((i: any, index: number) => {
        if (!(Number(i.parentCashbackAmt) > 0)) {
          i.cashbackAmt = 0;
          checkedServiceRules.value.push(i.configId);
        }
        serviceFeePolicyDTOList.forEach((j: any) => {
          if (j.configId === i.configId) {
            if (!(Number(j.parentCashbackAmt) > 0)) {
              j.cashbackAmt = 0;
            }
            oriServiceFeePolicyDTOList[index] = j;
            if (!checkedServiceRules.value.includes(j.configId)) {
              checkedServiceRules.value.push(j.configId);
            }
          }
        });
      });
      ruleForm.serviceFeePolicyDTOList = oriServiceFeePolicyDTOList;
    }

    // 设置流量费
    const simFeePolicyMap = oriSimFeeNewPolicyDTO;
    const simFeePolicyPeriodGroupMap = deepClone(simFeePolicyPeriodGroupDef);
    simFeePolicyPeriodGroupMap.forEach((period: any) => {
      Object.keys(simFeePolicyMap).forEach((key) => {
        if (period.field === key) {
          period.data = simFeePolicyMap[key];
          period.checkedList = [];
          period.data.forEach((pItem: any, pIndex: number) => {
            if (!(Number(pItem.parentCashbackAmt) > 0)) {
              pItem.cashbackAmt = 0;
              period.checkedList.push(pItem.configId);
            }
            simFeeNewPolicyDTO[key]?.forEach((sItem: any) => {
              if (pItem.configId === sItem.configId) {
                if (!(Number(sItem.parentCashbackAmt) > 0)) {
                  sItem.cashbackAmt = 0;
                }
                period.data[pIndex] = sItem;
                if (!period.checkedList.includes(sItem.configId)) {
                  period.checkedList.push(sItem.configId);
                }
              }
            });
          });
        }
      });
    });

    // 找出所有通道
    let allChannelCodes: any = [];
    try {
      const channelCodes = new Set(
        [...(ruleForm.serviceFeePolicyDTOList || []), ...Object.values(simFeePolicyMap || {})]
          .flat(Infinity)
          .flatMap(item => (Array.isArray(item) ? item.map(subItem => subItem?.channelCode) : item?.channelCode))
          .filter(code => code?.trim())
          .map(code => code.trim()),
      );
      allChannelCodes = [...channelCodes];
    }
    catch (error) {
      console.log(error);
    }

    const policyConfigByChannelGroup: IPolicyConfigByChannelGroupItem[] = [];
    allChannelCodes.forEach((item: any) => {
      const channelItem = channelCodes.value.find((channel: any) => channel.channelCode === item);
      policyConfigByChannelGroup.push({
        channelCode: item,
        channelName: channelItem?.channelName,
        channelData: deepClone(policyConfigByTerminalSourceDef),
      });
    });

    policyConfigByChannelGroup.forEach((channel: IPolicyConfigByChannelGroupItem) => {
      channel.channelData.forEach((tab) => {
        tab.serviceFeeData = ruleForm.serviceFeePolicyDTOList.filter((item: any) => item.channelCode === channel.channelCode && item.terminalSource === tab.key);

        const simFeeData = simFeePolicyPeriodGroupMap.map((period: any) => {
          return {
            ...period,
            data: period.data.filter((item: any) => item.channelCode === channel.channelCode && item.terminalSource === tab.key),
          };
        });
        tab.simFeeData = simFeeData;
      });
    });

    ruleForm.policyConfigByChannelGroup = policyConfigByChannelGroup;

    activeChannelTabKey.value = '0';
    activeTerminalSourceTabKey.value = '1';
  });
}

async function getChannelList() {
  const data = await CommonApi.getChannelList();
  channelCodes.value = data || [];
}

async function queryAddDisplay() {
  const data = await RatePolicyApi.queryAddDisplay();
  let { serviceFeePolicyDTOList, simFeeNewPolicyDTO } = data || {};
  serviceFeePolicyDTOList = serviceFeePolicyDTOList || [];
  simFeeNewPolicyDTO = simFeeNewPolicyDTO || {};

  if (!form.cashbackPolicyName && !isUpdate.value) {
    // 设置服务费
    checkedServiceRules.value = [];
    serviceFeePolicyDTOList.forEach((i: any) => {
      if (!(Number(i.parentCashbackAmt) > 0)) {
        i.cashbackAmt = 0;
        checkedServiceRules.value.push(i.configId);
      }
    });
    ruleForm.serviceFeePolicyDTOList = serviceFeePolicyDTOList;

    const simFeePolicyMap = simFeeNewPolicyDTO || {};
    const simFeePolicyPeriodGroupMap = deepClone(simFeePolicyPeriodGroupDef);
    simFeePolicyPeriodGroupMap.forEach((period: any) => {
      Object.keys(simFeePolicyMap).forEach((key) => {
        if (period.field === key) {
          period.data = simFeePolicyMap[key] || [];
          period.checkedList = [];
          period.data.forEach((item: any) => {
            if (!(Number(item.parentCashbackAmt) > 0)) {
              item.cashbackAmt = 0;
              period.checkedList.push(item.configId);
            }
          });
        }
      });
    });

    // 找出所有通道
    let allChannelCodes: any = [];
    try {
      const channelCodes = new Set(
        [...(ruleForm.serviceFeePolicyDTOList || []), ...Object.values(simFeeNewPolicyDTO || {})]
          .flat(Infinity)
          .flatMap(item => (Array.isArray(item) ? item.map(subItem => subItem?.channelCode) : item?.channelCode))
          .filter(code => code?.trim())
          .map(code => code.trim()),
      );
      allChannelCodes = [...channelCodes];
    }
    catch (error) {
      console.log(error);
    }

    const policyConfigByChannelGroup: IPolicyConfigByChannelGroupItem[] = [];
    allChannelCodes.forEach((item: any) => {
      const channelItem = channelCodes.value.find((channel: any) => channel.channelCode === item);
      policyConfigByChannelGroup.push({
        channelCode: item,
        channelName: channelItem?.channelName,
        channelData: deepClone(policyConfigByTerminalSourceDef),
      });
    });

    policyConfigByChannelGroup.forEach((channel: IPolicyConfigByChannelGroupItem) => {
      channel.channelData.forEach((tab) => {
        tab.serviceFeeData = ruleForm.serviceFeePolicyDTOList.filter((item: any) => item.channelCode === channel.channelCode && item.terminalSource === tab.key);

        const simFeeData = simFeePolicyPeriodGroupMap.map((period: any) => {
          return {
            ...period,
            data: period.data.filter((item: any) => item.channelCode === channel.channelCode && item.terminalSource === tab.key),
          };
        });
        tab.simFeeData = simFeeData;
      });
    });

    ruleForm.policyConfigByChannelGroup = policyConfigByChannelGroup;

    activeChannelTabKey.value = '0';
    activeTerminalSourceTabKey.value = '1';
  }

  oriPolicyDTOListMap.serviceFeePolicyDTOList = deepClone(serviceFeePolicyDTOList || []);
  oriPolicyDTOListMap.simFeeNewPolicyDTO = deepClone(simFeeNewPolicyDTO || {});
}

function simFeeCashbackAmtRules(key: any, maxCashbackAmt: any, period: any): FormItemRule[] {
  if (period.checkedList.includes(key)) {
    return [
      {
        required: true,
        message: '请输入',
        validator: async (value) => {
          if (Number(value) > Number(maxCashbackAmt)) {
            return Promise.reject(`注：下级必须低于或等于${maxCashbackAmt}元`);
          }
          return Promise.resolve();
        },
        trigger: 'blur',
      },
    ];
  }
  return [];
}

function serviceCashbackAmtRules(key: any, maxCashbackAmt: any): FormItemRule[] {
  if (checkedServiceRules.value.includes(key)) {
    return [
      {
        required: true,
        message: '请输入',
        validator: async (value) => {
          if (Number(value) > Number(maxCashbackAmt)) {
            return Promise.reject(`注：下级必须低于或等于${maxCashbackAmt}元`);
          }
          return Promise.resolve();
        },
        trigger: 'blur',
      },
    ];
  }
  return [];
}
</script>

<style lang="scss" scoped>
:deep(.custom-checkbox-group){
 background-color: transparent;

 .wd-checkbox{
   margin-bottom: 0;
  }
}

:deep(.custom-rule-group){
  .wd-cell__wrapper{
     justify-content: flex-start;
    }

  .rule-cell{
   .wd-cell__left{
    flex: none;
    flex-shrink: 0;
    max-width: 35%;
   }

   .wd-cell__right{
    flex: none;
   }

   .wd-input__inner{
    height: 30px;
   }

  .wd-input__body{
      width: 240rpx;
      text-align: center;
    }
  }

  .rule-desc{
    .wd-cell__label{
      margin-top: 0;
    }

    .wd-cell__right{
      flex: none;
    }
  }
}

:deep(.custom-rate-tabs){
  height: 100%;

  .wd-tabs__container{
    overflow-y: scroll;
    height: calc(100% - 42px);
  }
}
</style>
