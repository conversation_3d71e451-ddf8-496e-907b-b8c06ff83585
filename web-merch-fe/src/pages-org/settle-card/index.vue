<template>
  <view class="h-full overflow-y-scroll">
    <!-- 分割块 -->
    <view class="gap-primary" />

    <!-- 主体 -->
    <view class="p-20rpx">
      <view
        v-for="(item, key) in datasource" :key="key"
        class="mb-20rpx rounded-lg p-30rpx text-white even:bg-#fa4350 odd:bg-#f0883a"
      >
        <view class="flex items-center">
          <view class="shrink-0">
            <view class="size-80rpx rounded-full bg-white p-10rpx">
              <i class="i-mdi-bank-circle-outline size-full shrink-0 text-#846a27" />
            </view>
          </view>
          <view class="ml-30rpx flex grow flex-col items-start text-26rpx leading-relaxed">
            <text>{{ item.bankAccountName }}</text>
            <text>{{ item.bankName }}</text>
            <text>{{ item.accountType === 'S' ? '对私' : item.accountType === 'G' ? '对公' : '' }}</text>
            <text class="font-medium">
              {{ item.bankAccountNoMask }}
            </text>
          </view>
        </view>

        <view class="text-right">
          <wd-button type="info" size="small" custom-class="mr-20rpx" @click="updateBankCard(item)">
            编辑收款卡
          </wd-button>
          <wd-button v-if="isSelect" type="info" size="small" @click="onSelect(item)">
            选择
          </wd-button>
        </view>
      </view>

      <view v-if="cardOptType !== -1" class="p-40rpx">
        <wd-button type="primary" size="large" block @click="updateBankCard(null)">
          新增收款卡
        </wd-button>
      </view>
    </view>

    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import { useMessage } from 'wot-design-uni';
import { emitter } from '@/utils/emitter';
import { SettleCardApi } from '@/api-org/settle-card';
import { useUserStore } from '@/store';

const message = useMessage();

const loginUser = computed(() => useUserStore().info);

const isSelect = ref(false);

const datasource = ref<any>([]);

/**
 * -1 不能添加  1 对私 2 对公 3 对公和对私
 */
const cardOptType = ref<number>(-1);

onLoad((query) => {
  isSelect.value = query?.isSelect === '1';
});

onShow(() => {
  queryDataSource();
});

onUnmounted(() => {
  emitter.off('picker-bank-card');
});

async function queryDataSource() {
  const res = await SettleCardApi.findPage();
  datasource.value = res || [];

  const orgType = loginUser.value.orgType;
  const cardTypeEnumArr = Array.from(new Set(datasource.value.map((i: any) => i.accountType).filter((i: any) => !!i)));
  if (orgType !== 5) {
    if (cardTypeEnumArr.length) {
      if (cardTypeEnumArr.length === 2) {
        cardOptType.value = -1;
      }
      else {
        if (cardTypeEnumArr[0] === 'G') {
          cardOptType.value = 1;
        }
        else {
          cardOptType.value = 2;
        }
      }
    }
    else {
      cardOptType.value = 3;
    }
  }
  else {
    if (cardTypeEnumArr.length) {
      cardOptType.value = -1;
    }
    else {
      cardOptType.value = 1;
    }
  }
}

function onSelect(item: object) {
  emitter.emit('picker-bank-card', item);
  uni.navigateBack();
}

function updateBankCard(item?: any) {
  uni.navigateTo({
    url: `/pages-org/settle-card/update-settle-card?isUpdate=${item ? 1 : 0}&cardOptType=${cardOptType.value}&id=${item?.id}`,
  });
}
</script>

<style lang="scss" scoped>
:deep(.cell-group) {
  .wd-cell {
    @apply p-0;
  }

  .wd-cell__wrapper {
    @apply p-2px;
  }
}
</style>
