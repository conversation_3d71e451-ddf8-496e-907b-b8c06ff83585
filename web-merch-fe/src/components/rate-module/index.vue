<!-- eslint-disable vue/no-mutating-props -->
<template>
  <view>
    <view class="rate-module">
      <template v-if="rateItem.rateType === 1">
        <wd-cell title="线下收单费率信息(%)" />
        <wd-cell border />

        <wd-row :gutter="10">
          <wd-col :span="12">
            <wd-input
              v-model="rateItem.rateInfoDTO.creditRate" label="银联标准(贷)" type="digit" placeholder="请输入"
              :prop="makeProp('creditRate')"
              :rules="rateFromRules.rate"
              :disabled="readonly"
            />
          </wd-col>
        </wd-row>

        <wd-row :gutter="10">
          <wd-col :span="12">
            <wd-input
              v-model="rateItem.rateInfoDTO.debitRate" label="银联标准(借)" type="digit" placeholder="请输入"
              :prop="makeProp('debitRate')"
              :rules="rateFromRules.rate"
              :disabled="readonly"
            />
            <!-- <wd-input
              v-model="rateItem.rateInfoDTO.nfcCreditRate" label="银联云闪付(贷)" type="digit" placeholder="请输入"
              :prop="makeProp('nfcCreditRate')"
              :rules="rateFromRules.rate"
              :disabled="readonly"
            /> -->
            <wd-input
              v-model="rateItem.rateInfoDTO.wechatRate" label="扫码费率" type="digit" placeholder="请输入"
              :prop="makeProp('wechatRate')"
              :rules="rateFromRules.rate"
              :disabled="readonly"
              @input="onInputChangeWechatRate"
            />
          </wd-col>
          <wd-col :span="12">
            <wd-input
              v-model="rateItem.rateInfoDTO.debitFeeMax" label="银联标准封顶(借)" type="digit" placeholder="请输入"
              :prop="makeProp('debitFeeMax')"
              :rules="rateFromRules.amount"
              :disabled="readonly"
            />
            <!-- <wd-input
              v-model="rateItem.rateInfoDTO.nfcDebitRate" label="银联云闪付(借)" type="digit" placeholder="请输入"
              :prop="makeProp('nfcDebitRate')"
              :rules="rateFromRules.rate"
              :disabled="readonly"
            /> -->
            <!-- <wd-input
              v-model="rateItem.rateInfoDTO.aliPayRate" label="支付宝" type="digit" placeholder="请输入"
              :prop="makeProp('aliPayRate')"
              :rules="rateFromRules.rate"
              :disabled="readonly"
            /> -->
          </wd-col>
        </wd-row>

        <wd-row v-if="showD0SingleFee" :gutter="10">
          <wd-col :span="12">
            <wd-input
              v-model="rateItem.rateInfoDTO.creditQrD0SingleFee" label="D0附加单笔(贷)" type="digit" placeholder="请输入"
              :prop="makeProp('creditQrD0SingleFee')"
              :rules="rateFromRules.amount"
              :disabled="readonly"
            />
          </wd-col>
          <wd-col :span="12">
            <wd-input
              v-model="rateItem.rateInfoDTO.debitQrD0SingleFee" label="D0附加单笔(借)" type="digit" placeholder="请输入"
              :prop="makeProp('debitQrD0SingleFee')"
              :rules="rateFromRules.amount"
              :disabled="readonly"
            />
          </wd-col>
        </wd-row>
      </template>

      <template v-else-if="rateItem.rateType === 4">
        <wd-cell title="机构出款设置(%)" />
        <wd-cell border />

        <wd-row :gutter="10">
          <wd-col :span="12">
            <wd-input
              v-model="rateItem.rateInfoDTO.pfWalletPubWdRate" label="分润提现税率(公)" type="digit" placeholder="请输入"
              :prop="makeProp('pfWalletPubWdRate')"
              :rules="rateFromRules.rate"
              :disabled="readonly"
            />
            <wd-input
              v-model="rateItem.rateInfoDTO.pfWalletPriWdRate" label="分润提现税率(私)" type="digit" placeholder="请输入"
              :prop="makeProp('pfWalletPriWdRate')"
              :rules="rateFromRules.rate"
              :disabled="readonly"
            />
          </wd-col>
          <wd-col :span="12">
            <wd-input
              v-model="rateItem.rateInfoDTO.pfWalletPubWdSingleFee" label="分润提现单笔(公)" type="digit" placeholder="请输入"
              :prop="makeProp('pfWalletPubWdSingleFee')"
              :rules="rateFromRules.amount"
              :disabled="readonly"
            />
            <wd-input
              v-model="rateItem.rateInfoDTO.pfWalletPriWdSingleFee" label="分润提现单笔(私)" type="digit" placeholder="请输入"
              :prop="makeProp('pfWalletPriWdSingleFee')"
              :rules="rateFromRules.amount"
              :disabled="readonly"
            />
          </wd-col>
        </wd-row>

        <wd-row :gutter="10">
          <wd-col :span="12">
            <wd-input
              v-model="rateItem.rateInfoDTO.rewardWalletPubWdRate" label="返现提现税率(公)" type="digit" placeholder="请输入"
              :prop="makeProp('rewardWalletPubWdRate')"
              :rules="rateFromRules.rate"
              :disabled="readonly"
            />
            <wd-input
              v-model="rateItem.rateInfoDTO.rewardWalletPriWdRate" label="返现提现税率(私)" type="digit" placeholder="请输入"
              :prop="makeProp('rewardWalletPriWdRate')"
              :rules="rateFromRules.rate"
              :disabled="readonly"
            />
          </wd-col>
          <wd-col :span="12">
            <wd-input
              v-model="rateItem.rateInfoDTO.rewardWalletPubWdSingleFee" label="返现提现单笔(公)" type="digit" placeholder="请输入"
              :prop="makeProp('rewardWalletPubWdSingleFee')"
              :rules="rateFromRules.amount"
              :disabled="readonly"
            />
            <wd-input
              v-model="rateItem.rateInfoDTO.rewardWalletPriWdSingleFee" label="返现提现单笔(私)" type="digit" placeholder="请输入"
              :prop="makeProp('rewardWalletPriWdSingleFee')"
              :rules="rateFromRules.amount"
              :disabled="readonly"
            />
          </wd-col>
        </wd-row>
      </template>
    </view>
  </view>
</template>

<script lang="ts" setup>
import type { FormItemRule } from 'wot-design-uni/components/wd-form/types';

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
});

const props = defineProps({
  /*
   * 费率信息
   */
  rateItem: {
    type: Object as PropType<RateItem>,
    required: true as const,
  },
  /*
   * 是否只读
   */
  readonly: {
    type: Boolean,
    default: false,
  },
  /*
   * 表单prop前缀
   */
  formPropPrefix: {
    type: Array,
    default() {
      return [];
    },
  },
  /*
   * 费率校验规则
   */
  formRules: Object as PropType<RateFormRules>,
  /*
   * 是否显示D0单笔费用
   */
  showD0SingleFee: {
    type: Boolean,
    default: false,
  },
});

watch(
  () => props.rateItem, // 要监听的属性
  (val: any) => {
    if (props.rateItem.rateType === 1 && val?.rateInfoDTO) {
      setRateDefValue();
    }
  },
  {
    immediate: true, // 立即执行
  },
);

const makeProp = function (prop: string) {
  return [...props.formPropPrefix, 'rateInfoDTO', prop].join('.');
};

const rateFromRules: RateFormRules = props.formRules
  || {
    rate: [
      { required: true, pattern: /^\d+(\.\d{1,4})?$/, message: '正数且小数点后最多四位' },
    ],
    amount: [
      { required: true, pattern: /^\d+(\.\d{1,2})?$/, message: '正数且小数点后最多两位' },
    ],
  };

function onInputChangeWechatRate({ value }: { value: any }) {
  const rateKeys = ['nfcCreditRate', 'nfcDebitRate', 'wechatRate', 'aliPayRate'];
  rateKeys.forEach((key) => {
    // eslint-disable-next-line vue/no-mutating-props
    props.rateItem.rateInfoDTO[key] = value;
  });
}

function setRateDefValue() {
  onInputChangeWechatRate({ value: props.rateItem.rateInfoDTO.wechatRate });

  let rateKeys = ['creditQrD0Rate', 'debitQrD0Rate'];
  if (!props.showD0SingleFee) {
    rateKeys = [...rateKeys, 'creditQrD0SingleFee', 'debitQrD0SingleFee'];
  }
  rateKeys.forEach((key) => {
    // eslint-disable-next-line vue/no-mutating-props
    props.rateItem.rateInfoDTO[key] = 0;
  });
}

export type RateItem = {
  rateType: number;
  rateInfoDTO: Record<string, any>;
};

export type RateFormRules = {
  rate: FormItemRule[];
  amount: FormItemRule[];
};
</script>

<style lang="scss" scoped>
:deep(.rate-module){
  .wd-input.is-cell{
    padding: 0;
    margin-bottom: 12px;

    .wd-input__label{
      margin-right: 10px;
      width: auto;
      flex: 1;

      // 隐藏必填*号
      &.is-required{
        padding-left: 0;

        &::after{
          display: none;
        }
      }
    }

    .wd-input__body{
      width: 66px;
      text-align: center;
      background-color: #f3f5f7;
      border-radius: 4px;
      flex: none;
      flex-shrink: 0;
    }
  }
}
</style>
