import type { CommonParams, CommonResult } from '../common/types';
import { post } from '@/utils/request';

/**
 * @description 商户报备API
 */
export class MerchReportApi {
  /**
   * 进件分页列表
   */
  static findPage = (data: CommonParams) => post<CommonResult>({ url: '/app/merchantSignOrder/findPage', data, custom: {
    loading: false,
  } });

  /**
   * 进件详情
   */
  static detail = (data: CommonParams) => post<CommonResult>({ url: '/app/merchantSignOrder/detail', data });

  /**
   * 商户进件驳回重提（新流程）
   */
  static merchReportResubmit = (data: CommonParams) => post<CommonResult>({ url: '/app/merchReportResubmit', data });

  /**
   * 驳回重提信息查询
   */
  static queryResubmitInfo = (data: CommonParams) => post<CommonResult>({ url: '/app/report/queryResubmitInfo', data });

  /**
   * 入网实名认证（新流程）
   */
  static auth = (data: CommonParams) => post<CommonResult>({ url: '/app/report/auth', data });

  /**
   * 个体/企业商户入网
   */
  static enterpriseAuth = (data: CommonParams) => post<CommonResult>({ url: '/app/report/enterpriseAuth', data });

  /**
   * 商户信息审核单查询
   */
  static merchAudit = (data: CommonParams) => post<CommonResult>({ url: '/app/merchAudit/getOne', data });

  /**
   * 个人转企业商户报备（新流程）
   */
  static microToEnterReport = (data: CommonParams) => post<CommonResult>({ url: '/app/microToEnterReport', data });

  /**
   * 商户进件（新流程）
   */
  static merchReport = (data: CommonParams) => post<CommonResult>({ url: '/app/merchReport', data });

  /**
   * 通道认证二维码查询
   */
  static chnAuthQrcode = (data: CommonParams) => post<CommonResult>({ url: '/app/chnAuthQrcode/query', data });

  /**
   * 申报支付宝大额支付
   */
  static applyUnAliBigActivityReport = (data: CommonParams) => post<CommonResult>({ url: '/app/channelMerchant/applyUnAliBigActivityReport', data });

  /**
   * 通道商户费率信息查询
   */
  static queryChlMerchRateInfoPage = (data: CommonParams) => post<CommonResult>({ url: '/app/merch/queryChlMerchRateInfoPage', data });

  /**
   * 查询出登录商户所有已报备成功的通道商户结算信息
   */
  static queryChnMerchSettleInfo = () => post<CommonResult>({ url: '/app/settleBankCard/queryChnMerchSettleInfo' });

  /**
   * 变更结算卡（同步通道【包含新增/编辑卡信息】）
   */
  static changeSettleCard = (data: CommonParams) => post<CommonResult>({ url: '/app/chnMerchBankCard/edit', data });

  /**
   * 查询通道是否支持变更结算卡、修改费率
   */
  static supportChangeSettleCard = (data: CommonParams) => post<CommonResult>({ url: '/app/settleBankCard/supportChangeSettleCard', data });

  /**
   * 补充结算卡信息
   */
  static supplementCard = (data: CommonParams) => post<CommonResult>({ url: '/app/settleBankCard/supplementCard', data });

  /**
   * 查询待补充结算信息
   */
  static querySupplementCard = (data: CommonParams) => post<CommonResult>({ url: '/app/settleBankCard/querySupplementCard', data });

  /**
   * 查询待补充结算信息
   */
  static queryChnMerchSettleCardInfo = (data: CommonParams) => post<CommonResult>({ url: '/app/settleBankCard/queryChnMerchSettleCardInfo', data });

  /**
   * 修改商户简称
   */
  static uptChnMerchAbbr = (data: CommonParams) => post<CommonResult>({ url: '/app/uptChnMerchAbbr', data });

  /**
   * 通道商户列表
   */
  static findChnMerchList = (data: CommonParams) => post<CommonResult>({ url: '/app/chnMerch/findList', data });

  /**
   * 设置D0结算类型
   */
  static d0SettleTypeModify = (data: CommonParams) => post<CommonResult>({ url: '/app/chnMerch/d0SettleTypeModify', data });

  /**
   * D0结算申请开通
   */
  static d0SettleApply = (data: CommonParams) => post<CommonResult>({ url: '/app/chnMerch/faceAuthApplyCheck', data, custom: {
    loading: false,
  } });

  /**
   * D0结算申请开通
   */
  static d0SettleOpen = (data: CommonParams) => post<CommonResult>({ url: '/app/chnMerch/faceAuthApply', data });
}
