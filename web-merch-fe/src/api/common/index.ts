import type { AreaResult, CommonParams, CommonResult, UploadImageParams, UploadImageResult } from './types';
import { post } from '@/utils/request';

/**
 * @description 公共API
 */
export class CommonApi {
  /**
   * 图片上传
   */
  static uploadImages = (data: UploadImageParams) => post<UploadImageResult>({ url: '/app/uploadImages', data, custom: {
  // 是否需要登录(默认true)
    auth: true,
    // 是否需要加密(默认true)
    secret: false,
  } });

  /**
   * 短信发送
   */
  static sendSms = (data: CommonParams) => post<CommonResult>({ url: '/sms/send', data, custom: {
    auth: false,
  } });

  /**
   * 通道银联MCC（小微专用）
   */
  static unionMccByMicro = (data: CommonParams) => post<CommonResult>({ url: '/mcc/unionMccByMicro', data });

  /**
   * 店铺行业MCC（小微专用）
   */
  static storeMcc = (data: CommonParams) => post<CommonResult>({ url: '/storeMcc/listByMicro', data });

  /**
   * 通道类目
   */
  static channelMcc = (data: CommonParams) => post<CommonResult>({ url: '/mcc/channelMcc', data, custom: {
    loading: false,
  } });

  /**
   * MCC大类
   */
  static bigclass = () => post<CommonResult>({ url: '/mcc/bigclass' });

  /**
   * MCC小类
   */
  static subclass = (data: CommonParams) => post<CommonResult>({ url: '/mcc/subclass', data });

  /**
   * 查询省
   */
  static queryProvince = () => post<AreaResult[]>({ url: '/area/province', custom: {
    loading: false,
  } });

  /**
   * 查询市
   */
  static queryCity = (data: CommonParams) => post<AreaResult[]>({ url: '/area/city', data, custom: {
    loading: false,
  } });

  /**
   * 查询区县
   */
  static queryCountry = (data: CommonParams) => post<AreaResult[]>({ url: '/area/country', data, custom: {
    loading: false,
  } });

  /**
   * 查询银行类别
   */
  static queryBankType = (data: CommonParams) => post<CommonResult>({ url: '/bank/type', data });

  /**
   * 查询支行
   */
  static queryBankSub = (data: CommonParams) => post<CommonResult>({ url: '/bank/sub', data });

  /**
   * 查询卡BIN
   */
  static queryBankCardBin = (data: CommonParams) => post<CommonResult>({ url: '/bank/cardBin', data });

  /**
   * 查询bankTypeCode
   */
  static getBankTypeCodeList = (data: CommonParams) => post<CommonResult>({ url: '/bank/getBankTypeCodeList', data });

  /**
   * OCR身份证
   */
  static ocrIdCard = (data: CommonParams) => post<CommonResult>({ url: '/ocr/idcard', data, custom: {
    loading: false,
  } });

  /**
   * OCR营业执照
   */
  static ocrBusinessLicense = (data: CommonParams) => post<CommonResult>({ url: '/ocr/businessLicense', data, custom: {
    loading: false,
  } });

  /**
   * OCR银行卡
   */
  static ocrBankCard = (data: CommonParams) => post<CommonResult>({ url: '/ocr/bankcard', data, custom: {
    loading: false,
  } });

  /**
   * 获取支付通道
   */
  static getChannelList = (data?: CommonParams) => post<CommonResult>({ url: '/app/channel/list', data, custom: {
    loading: false,
  } });

  /**
   * 获取分润通道
   */
  static getRemitChannel = (data: CommonParams) => post<CommonResult>({ url: '/orgapp/remitChannelAll', data, custom: {
    loading: false,
  } });
}
