<template>
  <view class="h-full overflow-y-scroll">
    <view class="flex justify-center bg-primary py-15px">
      <view v-for="(item, key) in fileList" :key="key" class="basis-1/2">
        <GivenUpload
          v-model:file-data="item.fileData"
          :file-name="item.fileName"
          :placeholder="item.placeholder"
          custom-class="w-full "
          @choose="onChooseFile"
        />
      </view>
    </view>

    <wd-form ref="formRef" :model="form" :rules="rules">
      <wd-cell-group border>
        <wd-cell title="结算卡类型" title-width="100px" prop="accountType" center>
          <wd-radio-group
            v-model="form.accountType"
            shape="dot" inline
            custom-class="flex items-center justify-end"
          >
            <wd-radio v-if="form.accountType === 'S'" value="S">
              对私
            </wd-radio>
            <wd-radio v-if="form.accountType === 'G'" value="G">
              对公
            </wd-radio>
          </wd-radio-group>
        </wd-cell>
        <wd-input
          v-model="form.bankAccountNo" prop="bankAccountNo"
          label="银行卡卡号" label-width="88px"
          placeholder="上传照片识别或选择卡"
          clearable use-suffix-slot align-right
        >
          <template #suffix>
            <wd-tag type="primary" round @click="onSelectBankCard">
              选择
            </wd-tag>
          </template>
        </wd-input>
        <wd-cell
          title="开户行所在地" title-width="100px"
          prop="city" is-link
          @click="onSelectArea"
        >
          <wd-textarea
            v-model="bankArea"
            placeholder="请选择"
            auto-height no-border readonly
            custom-textarea-class="text-right"
          />
        </wd-cell>
        <wd-cell
          title="开户行总行" title-width="100px"
          prop="typeCode" is-link
          @click="onSelectBankType"
        >
          <wd-textarea
            v-model="form.bankName"
            placeholder="请选择"
            auto-height no-border readonly
            custom-textarea-class="text-right"
          />
        </wd-cell>
        <wd-cell
          title="开户行支行" title-width="100px"
          prop="bankBranch" is-link
          @click="onSelectBankSub"
        >
          <wd-textarea
            v-model="form.bankBranch"
            placeholder="请选择"
            auto-height no-border readonly
            custom-textarea-class="text-right"
          />
        </wd-cell>
        <wd-input
          v-model="form.mobile" prop="mobile"
          label="预留手机号" label-width="100px"
          placeholder="请输入预留手机号"
          align-right
        />
        <wd-cell />
      </wd-cell-group>
      <view class="p-40rpx">
        <wd-button type="primary" size="large" block @click="save">
          提交
        </wd-button>
      </view>
    </wd-form>
    <wd-toast />
  </view>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'wot-design-uni/components/wd-form/types';
import { useToast } from 'wot-design-uni';
import type { GivenUploadProps } from '@/components/given-upload/type';
import { CommonApi } from '@/api/common';
import { emitter } from '@/utils/emitter';
import { Toast, buildUrlWithParams, deepClone } from '@/utils';
import { MerchReportApi } from '@/api/report';
import { CHANNEL_CODE } from '@/config/setting';

const bankArea = ref('');

const toast = useToast();

// 表单
const formRef = ref<FormInstance | null>(null);
interface SettleCardInfo {
  chnMerchNo: string; // 通道商户号（必传）
  channelCode: string; // 通道编码（必传）
  bankAccountNo: string; // 银行账号
  mobile: string; // 预留手机号
  bankBranch: string; // 开户支行名称
  bankChannelNo: string; // 联行号
  province: string; // 开户行省编码
  city: string; // 开户行市编码
  cardType: number | null; // 卡类型不能为空 1:借记卡 2:贷记卡 3:准贷记卡 4:预付费卡
  bankName: string; // 银行名称
  typeCode: string; // 银行行别代码
  accountType: string; // 账户类型 S对私 G对公
  [key: string]: any;
}
const form: SettleCardInfo = reactive({
  chnMerchNo: '',
  channelCode: CHANNEL_CODE,
  bankAccountNo: '',
  mobile: '',
  bankBranch: '',
  bankChannelNo: '',
  province: '',
  city: '',
  cardType: null,
  bankName: '',
  typeCode: '',
  accountType: '',
});
// 规则
const rules: FormRules = {
  bankAccountNo: [{ required: true, message: '请输入银行卡卡号' }],
  bankBranch: [{ required: true, message: '请选择' }],
  city: [{ required: true, message: '请选择' }],
  typeCode: [{ required: true, message: '请选择' }],
  mobile: [{ required: true, pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }],
};

// 文件列表
const fileList = ref <GivenUploadProps[] > ([
  {
    fileName: '银行卡正面',
    placeholder: require('@/static/images/bank_card.png'),
    fileData: '',
    fileType: 3,
  },
]);

onLoad((query: any) => {
  form.chnMerchNo = query.chnMerchNo;
  form.accountType = query.accountType;
});

async function save() {
  // 检验表单
  const { valid, errors } = await formRef.value!.validate();
  if (!valid)
    return Promise.reject(errors);

  // 校验文件
  await checkFile();
  // 上传文件
  const imageJsonList = await uploadFile();
  form.imageJsonList = imageJsonList;

  // 默认借记卡
  form.cardType = form.cardType || 1;

  await MerchReportApi.changeSettleCard(form);
  toast.success({
    msg: '操作成功',
    closed: () => {
      uni.navigateBack();
    },
  });
}

/*
 * 校验文件
 */
async function checkFile() {
  const isCheckPass = fileList.value.every((f) => {
    if (f.show !== false && !f.fileData) {
      toast.warning(`请上传${f.fileName}`);
      return false;
    }
    return true;
  });
  if (!isCheckPass) {
    return Promise.reject(new Error('upload check fail !'));
  }
  return Promise.resolve();
}

/**
 * 上传文件
 */
async function uploadFile() {
  const fileListHasVal = fileList.value.filter(i => !!i.fileData);
  const noChangeFiles = [];
  const changeedFiles = [];
  fileListHasVal.forEach((i) => {
    if (/^(https?:)/.test(i.fileData)) {
      noChangeFiles.push({
        id: i.id,
        imageType: i.fileType,
        imagePath: i.fileData,
      });
    }
    else {
      changeedFiles.push({
        fileType: i.fileType,
        suffixType: 'png',
        fileData: i.fileData,
      });
    }
  });

  let imageJsonList = [];
  if (changeedFiles.length) {
    const data = await CommonApi.uploadImages({ channelCode: CHANNEL_CODE, imageList: changeedFiles })
      .catch(() => Promise.reject(new Error('upload fail!')));
    imageJsonList = data?.imageJsonList;
  }

  return [...noChangeFiles, ...imageJsonList];
}

async function onChooseFile(value: string) {
  const res = await CommonApi.ocrBankCard({ imgFile: value });
  form.bankAccountNo = res?.cardNum;
  const cardTypeMap: Record<string, number> = {
    DC: 1,
    CC: 2,
    SCC: 3,
    PC: 4,
  };
  form.cardType = cardTypeMap[res?.cardType] as any;
}

function onSelectBankCard() {
  uni.navigateTo({ url: `/pages/settle-info/debit-card/index?isSelect=1&accountType=${form.accountType || ''}` });
  emitter.on('picker-bank-card', (item: any) => {
    item = deepClone(item);
    Object.keys(form).forEach((key) => {
      form[key] = item[key] || form[key];
    });
    bankArea.value = [item.provinceName, item.cityName].join('');

    fileList.value.forEach((file) => {
      item.imageJsonList.forEach((file2) => {
        if (file2?.imageType === file.fileType) {
          file.fileData = file2?.imagePath;
          file.id = file2?.id;
        }
      });
    },
    );
  });
}

function onSelectArea() {
  uni.navigateTo({ url: '/pages/picker-view/area/index' });
  emitter.on('picker-area', (data: any) => {
    const [province, city] = data;
    bankArea.value = data.map((item: any) => item.areaName).join('');

    form.province = province.areaCode;
    form.city = city.areaCode;

    form.bankBranch = '';
    form.bankChannelNo = '';
  });
}

function onSelectBankType() {
  uni.navigateTo({ url: `/pages/picker-view/bank/bank-type` });
  emitter.on('picker-bank-type', (data: any) => {
    form.bankName = data.typeName;
    form.typeCode = data.typeCode;

    form.bankBranch = '';
    form.bankChannelNo = '';
  });
}

function onSelectBankSub() {
  const { typeCode, city, province } = form;
  if (!city)
    return Toast('请选择开户行所在地');
  if (!typeCode)
    return Toast('请选择开户行总行');

  const query = {
    typeCode,
    provinceCode: province,
    cityCode: city,
  };
  uni.navigateTo({
    url: `/pages/picker-view/bank/bank-sub?where=${encodeURIComponent(JSON.stringify(query))}`,
  });

  emitter.on('picker-bank-sub', (data: any) => {
    form.bankBranch = data.bankName;
    form.bankChannelNo = data.clearChannelNo;
  });
}
</script>

<style lang="scss" scoped>
:deep(){
  .wd-input__error-message,
  .wd-cell__error-message,
  .wd-textarea__error-message,
  .wd-picker__error-message
  {
    text-align: right !important;
  }
}
</style>
