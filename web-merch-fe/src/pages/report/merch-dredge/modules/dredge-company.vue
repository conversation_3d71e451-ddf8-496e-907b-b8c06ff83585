<template>
  <view class="form-wrapper">
    <wd-form ref="formRef" :model="form" :rules="rules">
      <!-- 企业信息 -->
      <wd-cell-group title="企业信息" border>
        <view class="flex flex-wrap justify-center bg-primary py-15px">
          <view v-for="(item, key) in [fileMap[7]]" :key="key" class="basis-1/2">
            <GivenUpload
              v-model:file-data="item.fileData"
              :file-name="item.fileName"
              :placeholder="item.placeholder"
              custom-class="w-full"
              @choose="onChooseFile"
            />
          </view>
        </view>

        <wd-input
          v-model="form.licenseName" prop="licenseName"
          label="企业名称" placeholder="可图片自动识别" label-width="100px"
          clearable align-right
        />
        <wd-textarea
          v-model="form.licenseNo" prop="licenseNo"
          label="统一社会信用代码" placeholder="可图片自动识别"
          clearable auto-height
          custom-textarea-class="text-right"
        />
        <wd-textarea
          v-model="form.licenseAddr" prop="licenseAddr"
          label="经营详细地址" placeholder="可图片自动识别" label-width="100px"
          clearable auto-height
          custom-textarea-class="text-right"
        />
        <wd-datetime-picker
          v-model="licenseDateRegion" prop="licenseEndDate"
          label="营业证件有效期"
          type="date" align-right
          :default-value="defaultDateRegionValue"
          :min-date="minDate"
          :max-date="maxDate"
          @confirm="onConfirmLicenseDateRegion"
        />
        <wd-input
          v-model="form.legalName" prop="legalName"
          label="法人姓名" placeholder="可图片自动识别" label-width="100px"
          clearable align-right
        />
        <wd-input
          v-if="form.isLegalSettle === 0"
          v-model="form.legalCertNo" prop="legalCertNo"
          :rules="[{ required: true, pattern: idCardFormat, message: '请正确输入身份证号' }]"
          label="法人身份证号" placeholder="请输入法人身份证号" label-width="100px"
          clearable align-right
        />
      </wd-cell-group>

      <wd-cell-group title="门店信息" border custom-class="mt-10px">
        <wd-input
          v-model="form.shortName" prop="shortName"
          label="商户简称" placeholder="请输入4-6位字符,不要出现省市区" label-width="100px"
          clearable align-right
        />
        <wd-cell
          title="行业类别" title-width="100px" prop="unionMcc" is-link
          @click="onSelectUnionMcc"
        >
          <wd-textarea
            v-model="form.unionMccName"
            placeholder="请选择行业MCC"
            auto-height no-border readonly
            custom-textarea-class="text-right"
          />
        </wd-cell>
      </wd-cell-group>

      <view class="p-12px text-26rpx font-medium">
        资质图片上传
      </view>
      <view class="flex flex-wrap">
        <view
          v-for="(item, key) in
            [
              fileMap[11], fileMap[12], fileMap[13],
              fileMap[6], fileMap[15],
            ]"
          :key="key"
          class="mb-12px basis-1/2"
        >
          <GivenUpload
            v-model:file-data="item.fileData"
            :file-name="item.fileName"
            :placeholder="item.placeholder"
            custom-class="w-full"
          />
        </view>
      </view>
    </wd-form>

    <wd-form ref="settleFormRef" :model="settleForm" :rules="settleRules" custom-class="error-message__align-right mt-10px">
      <wd-cell-group title="结算信息" border>
        <wd-cell title="账户类型" title-width="100px" prop="accountType" center>
          <wd-radio-group
            v-model="form.accountType"
            shape="dot" inline
            custom-class="flex items-center justify-end"
            @change="onChangeAccountType"
          >
            <wd-radio value="S">
              对私结算
            </wd-radio>
            <wd-radio v-if="merchGrade === 'A'" value="G">
              对公结算
            </wd-radio>
          </wd-radio-group>
        </wd-cell>
        <wd-cell v-if="form.accountType === 'S'" title="结算人类型" title-width="100px" prop="isLegalSettle" center>
          <wd-radio-group
            v-model="form.isLegalSettle"
            shape="dot" inline
            custom-class="flex items-center justify-end"
          >
            <wd-radio :value="1">
              法人结算
            </wd-radio>
            <!-- <wd-radio :value="0">
              非法人结算
            </wd-radio> -->
          </wd-radio-group>
        </wd-cell>
        <wd-cell />
        <view class="flex justify-center bg-white py-15px">
          <view v-for="(item, key) in [fileMap[3]]" :key="key" class="basis-1/2">
            <GivenUpload
              v-model:file-data="item.fileData"
              :file-name="item.fileName"
              :placeholder="item.placeholder"
              custom-class="w-full "
              @choose="onChooseFile1"
              @remove="onRemoveFile"
            />
          </view>
        </view>
        <wd-input
          :model-value="merchInfo?.merchantDetail?.legalName"
          label="银行账户名称" label-width="100px"
          placeholder="--"
          align-right
          readonly
        />
        <wd-input
          v-model="settleForm.bankAccountNo" prop="bankAccountNo"
          label="银行卡卡号" label-width="88px"
          placeholder="上传照片识别或选择卡"
          clearable use-suffix-slot align-right
          :readonly="isBanEditSettle"
        >
          <template #suffix>
            <wd-tag type="primary" round @click="onSelectBankCard">
              选择
            </wd-tag>
          </template>
        </wd-input>
        <wd-cell
          title="开户行所在地" title-width="100px"
          prop="city" :is-link="!isBanEditSettle"
          @click="onSelectArea"
        >
          <wd-textarea
            v-model="bankArea"
            placeholder="请选择"
            auto-height no-border readonly
            custom-textarea-class="text-right"
          />
        </wd-cell>
        <wd-cell
          title="开户行总行" title-width="100px"
          prop="typeCode" :is-link="!isBanEditSettle"
          @click="onSelectBankType"
        >
          <wd-textarea
            v-model="settleForm.bankName"
            placeholder="请选择"
            auto-height no-border readonly
            custom-textarea-class="text-right"
          />
        </wd-cell>
        <wd-cell
          title="开户行支行" title-width="100px"
          prop="bankBranch" :is-link="!isBanEditSettle"
          @click="onSelectBankSub"
        >
          <wd-textarea
            v-model="settleForm.bankBranch"
            placeholder="请选择"
            auto-height no-border readonly
            custom-textarea-class="text-right"
          />
        </wd-cell>
        <wd-input
          v-model="settleForm.mobile" prop="mobile"
          label="预留手机号" label-width="100px"
          placeholder="请输入预留手机号"
          align-right
          :readonly="isBanEditSettle"
        />
      </wd-cell-group>

      <view class="p-40rpx">
        <wd-button type="primary" size="large" block @click="save">
          提交
        </wd-button>
      </view>
    </wd-form>

    <!-- 挂载点 -->
    <wd-toast />
    <wd-message-box :close-on-click-modal="false" />
  </view>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'wot-design-uni/components/wd-form/types';
import { useMessage, useToast } from 'wot-design-uni';
import dayjs from 'dayjs';
import type { GivenUploadProps } from '@/components/given-upload/type';
import { CommonApi } from '@/api/common/index';
import { CHANNEL_CODE } from '@/config/setting';
import { emitter } from '@/utils/emitter';
import { MerchReportApi } from '@/api/report';
import { Toast, deepClone } from '@/utils';

const props = defineProps({
  merchInfo: {
    type: Object,
  },
  handleType: String,
});

const toast = useToast();
const message = useMessage();

const licenseDateRegion = ref<any>([]);
const defaultDateRegionValue = ref([dayjs().valueOf(), dayjs().valueOf()]);

// 营业执照有效期选择范围
const minDate = dayjs().subtract(50, 'year').valueOf();
const maxDate = dayjs('2099-12-31').valueOf();

// 表单
const form = reactive<any>({
  isMicro: 0,
  isLegalSettle: 1,
  accountType: 'S',
  channelCode: CHANNEL_CODE,
  merchantNo: props.merchInfo?.merchant?.merchantNo,
});

// 校验规则
const validatorShortName = (val: string) => {
  if (String(val).length >= 4 && String(val).length <= 6) {
    return Promise.resolve();
  }
  else {
    return Promise.reject();
  }
};
const idCardFormat: RegExp = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9X]$/i;
const rules: FormRules = {
  shortName: [{ required: true, validator: validatorShortName, message: '请输入4-6位字符' }],
  unionMcc: [{ required: true, message: '请选择行业类别' }],

  licenseName: [{ required: true, message: '请输入企业名称' }],
  licenseNo: [{ required: true, message: '请输入统一社会信用代码' }],
  licenseAddr: [{ required: true, message: '请输入经营详细地址' }],
  licenseEndDate: [{ required: true, message: '请选择营业证件有效期' }],
  legalName: [{ required: true, message: '请输入法人姓名' }],
};

const formRef = ref<FormInstance | null>(null);

const settleForm = reactive<any>({
  bankAccountNo: '',
  mobile: '',
  bankBranch: '',
  bankChannelNo: '',
  province: '',
  city: '',
  cardType: null,
  bankName: '',
  typeCode: '',
  accountType: 'S',
});
const settleRules: FormRules = {
  bankAccountNo: [{ required: true, message: '请输入银行卡卡号' }],
  bankBranch: [{ required: true, message: '请选择' }],
  city: [{ required: true, message: '请选择' }],
  typeCode: [{ required: true, message: '请选择' }],
  mobile: [{ required: true, pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }],
};

const bankArea = ref('');
const settleFormRef = ref<FormInstance | null>(null);

const isBanEditSettle = ref(false);

function onRemoveFile() {
  isBanEditSettle.value = false;
  Object.keys(settleForm).forEach((key) => {
    settleForm[key] = '';
  });
  bankArea.value = '';
  settleForm.accountType = form.accountType;
}

// 文件
type FileType = 11 | 12 | 13 | 7 | 6 | 15 | 3;
const fileMap = ref <Record<FileType, GivenUploadProps>> ({
  7: {
    fileName: '营业执照照片',
    placeholder: require('@/static/images/bank_card.png'),
    fileData: '',
    fileType: 7,
  },
  11: {
    fileName: '门店门头照',
    placeholder: require('@/static/images/door_photo.png'),
    fileData: '',
    fileType: 11,
  },
  12: {
    fileName: '店内环境照片',
    placeholder: require('@/static/images/store_environment_photo.png'),
    fileData: '',
    fileType: 12,
  },
  13: {
    fileName: '收银台照',
    placeholder: require('@/static/images/cashier_photo.png'),
    fileData: '',
    fileType: 13,
  },
  6: {
    fileName: '手持证件照',
    placeholder: require('@/static/images/card_hand.png'),
    fileData: '',
    fileType: 6,
  },
  15: {
    fileName: '商户协议照',
    placeholder: require('@/static/images/bank_card.png'),
    fileData: '',
    fileType: 15,
  },
  3: {
    fileName: '银行卡正面',
    placeholder: require('@/static/images/bank_card.png'),
    fileData: '',
    fileType: 3,
  },
});

const merchGrade = ref('');

async function queryResubmitInfo() {
  const res = await MerchReportApi.queryResubmitInfo({ id: props.merchInfo?.id });
  Object.assign(form, res);
  form.shortName = res?.merchantAbbr;
  form.id = props.merchInfo?.id;
  merchGrade.value = res?.merchantGrade;

  if (res?.settleBankCardDTO) {
    const item = res.settleBankCardDTO;
    Object.keys(settleForm).forEach((key) => {
      settleForm[key] = item[key] || settleForm[key];
    });
    form.accountType = item?.accountType;
    bankArea.value = [item.provinceName, item.cityName].join('');

    if (res?.settleBankCardDTO?.imageJsonList) {
      res.imageList = [...res.imageList, ...res?.settleBankCardDTO?.imageJsonList];
    }
  }
  if (res?.imageList?.length) {
    const fileList = Object.values(fileMap.value);
    fileList.forEach((item) => {
      res.imageList.forEach((item2: any) => {
        if (item2.imageType === item.fileType) {
          item.fileData = item2?.imagePath;
          item.id = item2?.id;
        }
      });
    });
  }

  if (res?.licenseStartDate && res?.licenseEndDate) {
    licenseDateRegion.value = [dayjs(res.licenseStartDate).valueOf(), dayjs(res.licenseEndDate).valueOf()];
  }

  form.merchantBankCard = form.imageList = null;
}

onMounted(() => {
  merchGrade.value = props.merchInfo?.merchant?.grade;
  form.accountType = 'S';
  form.termSn = props.merchInfo?.terminalSn;
  if (props.handleType === '3') {
    queryResubmitInfo();
  }
});

async function save() {
  // 检验表单
  const { valid: valid2, errors: errors2 } = await formRef.value!.validate();
  if (!valid2)
    return Promise.reject(errors2);

  // 校验表单
  const { valid, errors } = await settleFormRef.value!.validate();
  if (!valid)
    return Promise.reject(errors);

  // 校验文件
  await checkFile();
  // 上传文件
  const imageJsonList = await uploadFile();

  form.imageJsonList = imageJsonList.filter(item => item.imageType !== 3);

  form.settleBankCardDTO = deepClone(settleForm);
  form.settleBankCardDTO.imageJsonList = imageJsonList.filter(item => item.imageType === 3);

  let result;
  if (props.handleType === '3') {
    result = MerchReportApi.merchReportResubmit;
  }
  else {
    result = MerchReportApi.merchReport;
  }
  const data = await result(form);
  // 进件成功
  if (data?.code) {
    toast.success({
      msg: data?.message,
      closed: () => {
        uni.navigateTo({ url: '/pages/report/merch-report/index' });
      },
    });
  }
  // 进件失败
  else {
    message.alert({
      title: '提示',
      msg: data?.message,
    }).then(() => {
      uni.navigateTo({ url: '/pages/report/merch-report/index' });
    });
  }
}

function onChangeAccountType() {
  if (form.accountType === 'S') {
    form.isLegalSettle = 1;
  }
  settleForm.accountType = form.accountType;
}

/*
 * 校验文件
 */
async function checkFile() {
  const fileList = Object.values(fileMap.value);
  const isCheckPass = fileList.every((f) => {
    if (f.show !== false && !f.fileData) {
      toast.warning(`请上传${f.fileName}`);
      return false;
    }
    return true;
  });
  if (!isCheckPass) {
    return Promise.reject(new Error('upload check fail !'));
  }
  return Promise.resolve();
}

async function onChooseFile1(value: string) {
  const res = await CommonApi.ocrBankCard({ imgFile: value });
  settleForm.bankAccountNo = res?.cardNum;
  const cardTypeMap: Record<string, number> = {
    DC: 1,
    CC: 2,
    SCC: 3,
    PC: 4,
  };
  settleForm.cardType = cardTypeMap[res?.cardType] as any;
}

/**
 * 上传文件
 */
async function uploadFile() {
  const fileList = Object.values(fileMap.value);
  const fileListHasVal = fileList.filter(i => !!i.fileData);
  const noChangeFiles: any = [];
  const changeedFiles: any = [];
  fileListHasVal.forEach((i: GivenUploadProps) => {
    if (/^(https?:)/.test(i.fileData as string)) {
      noChangeFiles.push({
        id: i.id,
        imageType: i.fileType,
        imagePath: i.fileData,
      });
    }
    else {
      changeedFiles.push({
        fileType: i.fileType,
        suffixType: 'png',
        fileData: i.fileData,
      });
    }
  });

  let imageJsonList: any = [];
  if (changeedFiles.length) {
    const data = await CommonApi.uploadImages({ channelCode: CHANNEL_CODE, imageList: changeedFiles })
      .catch(() => Promise.reject(new Error('upload fail!')));
    imageJsonList = data?.imageJsonList;
  }

  return [...noChangeFiles, ...imageJsonList];
}

function onConfirmLicenseDateRegion() {
  const [startDate, endDate] = licenseDateRegion.value;
  form.licenseStartDate = dayjs(startDate).format('YYYY-MM-DD');
  form.licenseEndDate = dayjs(endDate).format('YYYY-MM-DD');
}

/** 选择营业执照&ocr */
async function onChooseFile(value: string) {
  const res = await CommonApi.ocrBusinessLicense({ imgFile: value });
  if (res?.success) {
    Object.keys(res).forEach((key) => {
      res[key] = res[key] === 'FailInRecognition' ? '' : res[key];
    });

    const { regNum, name, person, address, establishDate, validPeriod } = res;
    form.licenseName = name;
    form.licenseNo = regNum;
    form.licenseAddr = address;
    form.legalName = person;

    if (establishDate && validPeriod) {
      let endDate = validPeriod;
      if (validPeriod === '长期') {
        endDate = '2099-12-31';
      }
      licenseDateRegion.value = [dayjs(establishDate).valueOf(), dayjs(endDate).valueOf()];
      onConfirmLicenseDateRegion();
    }
  }
}

function onSelectUnionMcc() {
  uni.navigateTo({ url: '/pages/picker-view/category/channel-category' });
  emitter.on('picker-category', (item) => {
    form.unionMcc = item.mcc;
    form.unionMccName = item.mccName;
  });
}

function onSelectBankCard() {
  uni.navigateTo({ url: `/pages/settle-info/debit-card/index?isSelect=1&accountType=${form.accountType || ''}` });
  emitter.on('picker-bank-card', (item: any) => {
    isBanEditSettle.value = true;
    item = deepClone(item);
    Object.keys(settleForm).forEach((key) => {
      settleForm[key] = item[key] || settleForm[key];
    });

    settleForm.accountType = form.accountType;

    // form.bankCardId = item.id;

    bankArea.value = [item.provinceName, item.cityName].join('');

    const fileList = Object.values(fileMap.value);
    fileList.forEach((file) => {
      item.imageJsonList.forEach((file2) => {
        if (file2?.imageType === file.fileType) {
          file.fileData = file2?.imagePath;
          file.id = file2?.id;
        }
      });
    },
    );
  });
}

function onSelectArea() {
  uni.navigateTo({ url: '/pages/picker-view/area/index' });
  emitter.on('picker-area', (data: any) => {
    const [province, city] = data;
    const areaName = data.map((item: any) => item.areaName).join('');
    if (areaName !== bankArea.value) {
      settleForm.bankBranch = '';
      settleForm.bankChannelNo = '';
    }
    bankArea.value = areaName;
    settleForm.province = province.areaCode;
    settleForm.city = city.areaCode;
  });
}

function onSelectBankType() {
  uni.navigateTo({ url: `/pages/picker-view/bank/bank-type` });
  emitter.on('picker-bank-type', (data: any) => {
    if (settleForm.typeCode !== data.typeCode) {
      settleForm.bankBranch = '';
      settleForm.bankChannelNo = '';
    }
    settleForm.bankName = data.typeName;
    settleForm.typeCode = data.typeCode;
  });
}

function onSelectBankSub() {
  const { typeCode, city, province } = settleForm;
  if (!city)
    return Toast('请选择开户行所在地');
  if (!typeCode)
    return Toast('请选择开户行总行');

  const query = {
    typeCode,
    provinceCode: province,
    cityCode: city,
  };
  uni.navigateTo({
    url: `/pages/picker-view/bank/bank-sub?where=${encodeURIComponent(JSON.stringify(query))}`,
  });

  emitter.on('picker-bank-sub', (data: any) => {
    settleForm.bankBranch = data.bankName;
    settleForm.bankChannelNo = data.clearChannelNo;
  });
}
</script>

<style lang="scss" scoped>
:deep(){
  .wd-input__error-message,
  .wd-cell__error-message,
  .wd-textarea__error-message,
  .wd-picker__error-message
  {
    text-align: right !important;
  }
}
</style>
