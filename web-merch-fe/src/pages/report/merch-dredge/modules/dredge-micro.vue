<template>
  <view>
    <wd-form ref="formRef" :model="form" :rules="rules" custom-class="error-message__align-right">
      <wd-cell-group title="门店信息" border>
        <wd-input
          v-model="form.shortName" prop="shortName"
          label="商户简称" placeholder="请输入4-6位字符,不要出现省市区" label-width="100px"
          clearable align-right
        />
        <wd-cell
          title="行业类别" title-width="100px" prop="unionMcc" is-link
          @click="onSelectUnionMcc"
        >
          <wd-textarea
            v-model="form.unionMccName"
            placeholder="请选择行业MCC"
            auto-height no-border readonly
            custom-textarea-class="text-right"
          />
        </wd-cell>

        <wd-cell title="门店图片信息" required />
        <view class="flex flex-wrap">
          <view v-for="(item, key) in [fileList[0]]" :key="key" class="mb-12px basis-1/2">
            <GivenUpload
              v-model:file-data="item.fileData"
              :file-name="item.fileName"
              :placeholder="item.placeholder"
              custom-class="w-full"
            />
          </view>
        </view>
      </wd-cell-group>
    </wd-form>

    <wd-form ref="settleFormRef" :model="settleForm" :rules="settleRules" custom-class="error-message__align-right  mt-10px">
      <wd-cell-group title="结算信息" border>
        <view class="flex justify-center bg-white py-15px">
          <view v-for="(item, key) in [fileList[1]]" :key="key" class="basis-1/2">
            <GivenUpload
              v-model:file-data="item.fileData"
              :file-name="item.fileName"
              :placeholder="item.placeholder"
              custom-class="w-full "
              @choose="onChooseFile"
              @remove="onRemoveFile"
            />
          </view>
        </view>
        <wd-input
          :model-value="merchInfo?.merchantDetail?.legalName"
          label="银行账户名称" label-width="100px"
          placeholder="--"
          align-right
          readonly
        />
        <wd-input
          v-model="settleForm.bankAccountNo" prop="bankAccountNo"
          label="银行卡卡号" label-width="88px"
          placeholder="上传照片识别或选择卡"
          clearable use-suffix-slot align-right
          :readonly="isBanEditSettle"
        >
          <template #suffix>
            <wd-tag type="primary" round @click="onSelectBankCard">
              选择
            </wd-tag>
          </template>
        </wd-input>
        <wd-cell
          title="开户行所在地" title-width="100px"
          prop="city" :is-link="!isBanEditSettle"
          @click="onSelectArea"
        >
          <wd-textarea
            v-model="bankArea"
            placeholder="请选择"
            auto-height no-border readonly
            custom-textarea-class="text-right"
          />
        </wd-cell>
        <wd-cell
          title="开户行总行" title-width="100px"
          prop="typeCode" :is-link="!isBanEditSettle"
          @click="onSelectBankType"
        >
          <wd-textarea
            v-model="settleForm.bankName"
            placeholder="请选择"
            auto-height no-border readonly
            custom-textarea-class="text-right"
          />
        </wd-cell>
        <wd-cell
          title="开户行支行" title-width="100px"
          prop="bankBranch" :is-link="!isBanEditSettle"
          @click="onSelectBankSub"
        >
          <wd-textarea
            v-model="settleForm.bankBranch"
            placeholder="请选择"
            auto-height no-border readonly
            custom-textarea-class="text-right"
          />
        </wd-cell>
        <wd-input
          v-model="settleForm.mobile" prop="mobile"
          label="预留手机号" label-width="100px"
          placeholder="请输入预留手机号"
          align-right
          :readonly="isBanEditSettle"
        />
      </wd-cell-group>

      <view class="p-40rpx">
        <wd-button type="primary" size="large" block @click="onDebounceSave">
          提交
        </wd-button>
      </view>
    </wd-form>
    <wd-toast />
    <wd-message-box :close-on-click-modal="false" />
  </view>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'wot-design-uni/components/wd-form/types';
import { useMessage, useToast } from 'wot-design-uni';
import { debounce } from 'wot-design-uni/components/common/util';
import type { GivenUploadProps } from '@/components/given-upload/type';
import { CommonApi } from '@/api/common/index';
import { CHANNEL_CODE } from '@/config/setting';
import { emitter } from '@/utils/emitter';
import { MerchReportApi } from '@/api/report';
import { Toast, deepClone } from '@/utils';

const props = defineProps({
  merchInfo: {
    type: Object,
  },
  handleType: String,
});

const toast = useToast();
const message = useMessage();

// 表单
const form = reactive<any>({
  isMicro: 1,
  channelCode: CHANNEL_CODE,
  merchantNo: props.merchInfo?.merchant?.merchantNo,
});
// 校验规则
const validatorShortName = (val: string) => {
  if (String(val).length >= 4 && String(val).length <= 6) {
    return Promise.resolve();
  }
  else {
    return Promise.reject('请输入4-6位字符');
  }
};
const rules: FormRules = {
  shortName: [{ required: true, validator: validatorShortName, message: '请输入4-6位字符' }],
  unionMcc: [{ required: true, message: '请选择行业类别' }],
};
const formRef = ref<FormInstance | null>(null);

const settleForm = reactive<any>({
  bankAccountNo: '',
  mobile: '',
  bankBranch: '',
  bankChannelNo: '',
  province: '',
  city: '',
  cardType: null,
  bankName: '',
  typeCode: '',
  accountType: 'S',
});
const settleRules: FormRules = {
  bankAccountNo: [{ required: true, message: '请输入银行卡卡号' }],
  bankBranch: [{ required: true, message: '请选择' }],
  city: [{ required: true, message: '请选择' }],
  typeCode: [{ required: true, message: '请选择' }],
  mobile: [{ required: true, pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }],
};

const bankArea = ref('');
const settleFormRef = ref<FormInstance | null>(null);
const isBanEditSettle = ref(false);

// 文件列表
const fileList = ref <GivenUploadProps[] > ([
  {
    fileName: '门头照',
    placeholder: require('@/static/images/door_photo.png'),
    fileData: '',
    fileType: 11,
  },
  // {
  //   fileName: '手持证件照',
  //   placeholder: require('@/static/images/card_hand.png'),
  //   fileData: '',
  //   fileType: 6,
  // },
  // {
  //   fileName: '商户协议照',
  //   placeholder: require('@/static/images/bank_card.png'),
  //   fileData: '',
  //   fileType: 15,
  // },
  {
    fileName: '银行卡正面',
    placeholder: require('@/static/images/bank_card.png'),
    fileData: '',
    fileType: 3,
  },
]);

async function queryResubmitInfo() {
  const res = await MerchReportApi.queryResubmitInfo({ id: props.merchInfo?.id });
  Object.assign(form, res);
  form.shortName = res?.merchantAbbr;
  form.id = props.merchInfo?.id;

  if (res?.settleBankCardDTO) {
    const item = res.settleBankCardDTO;
    Object.keys(settleForm).forEach((key) => {
      settleForm[key] = item[key] || settleForm[key];
    });
    bankArea.value = [item.provinceName, item.cityName].join('');

    if (res?.settleBankCardDTO?.imageJsonList) {
      res.imageList = [...res.imageList, ...res?.settleBankCardDTO?.imageJsonList];
    }
  }
  if (res?.imageList?.length) {
    fileList.value.forEach((item) => {
      res?.imageList.forEach((item2) => {
        if (item2?.imageType === item.fileType) {
          item.fileData = item2?.imagePath;
          item.id = item2?.id;
        }
      });
    });
  }

  form.merchantBankCard = form.imageList = null;
}

onMounted(() => {
  form.termSn = props.merchInfo?.terminalSn;
  if (props.handleType === '3') {
    queryResubmitInfo();
  }
});

const onDebounceSave = debounce(() => {
  save();
}, 500);
async function save() {
  // 检验表单
  const { valid: valid2, errors: errors2 } = await formRef.value!.validate();
  if (!valid2)
    return Promise.reject(errors2);

  // 校验表单
  const { valid, errors } = await settleFormRef.value!.validate();
  if (!valid)
    return Promise.reject(errors);

  // 校验文件
  await checkFile();
  // 上传文件
  const imageJsonList = await uploadFile();

  form.imageJsonList = imageJsonList.filter(item => item.imageType !== 3);

  form.settleBankCardDTO = deepClone(settleForm);
  form.settleBankCardDTO.imageJsonList = imageJsonList.filter(item => item.imageType === 3);

  let result;
  if (props.handleType === '3') {
    result = MerchReportApi.merchReportResubmit;
  }
  else {
    result = MerchReportApi.merchReport;
  }
  const data = await result(form);

  // 进件成功
  if (data?.code) {
    toast.success({
      msg: data?.message,
      closed: () => {
        uni.navigateTo({ url: '/pages/report/merch-report/index' });
      },
    });
  }
  // 进件失败
  else {
    message.alert({
      title: '提示',
      msg: data?.message,
    }).then(() => {
      uni.navigateTo({ url: '/pages/report/merch-report/index' });
    });
  }
}

async function onChooseFile(value: string) {
  const res = await CommonApi.ocrBankCard({ imgFile: value });
  settleForm.bankAccountNo = res?.cardNum;
  const cardTypeMap: Record<string, number> = {
    DC: 1,
    CC: 2,
    SCC: 3,
    PC: 4,
  };
  settleForm.cardType = cardTypeMap[res?.cardType] as any;
}

/*
 * 校验文件
 */
async function checkFile() {
  const isCheckPass = fileList.value.every((f) => {
    if (!f.fileData) {
      toast.warning(`请上传${f.fileName}`);
      return false;
    }
    return true;
  });
  if (!isCheckPass) {
    return Promise.reject(new Error('upload check fail !'));
  }
  return Promise.resolve();
}

/**
 * 上传文件
 */
async function uploadFile() {
  const fileListHasVal = fileList.value.filter(i => !!i.fileData);
  const noChangeFiles = [];
  const changeedFiles = [];
  fileListHasVal.forEach((i) => {
    if (/^(https?:)/.test(i.fileData)) {
      noChangeFiles.push({
        id: i.id,
        imageType: i.fileType,
        imagePath: i.fileData,
      });
    }
    else {
      changeedFiles.push({
        fileType: i.fileType,
        suffixType: 'png',
        fileData: i.fileData,
      });
    }
  });

  let imageJsonList = [];
  if (changeedFiles.length) {
    const data = await CommonApi.uploadImages({ channelCode: CHANNEL_CODE, imageList: changeedFiles })
      .catch(() => Promise.reject(new Error('upload fail!')));
    imageJsonList = data?.imageJsonList;
  }

  return [...noChangeFiles, ...imageJsonList];
}

function onRemoveFile() {
  isBanEditSettle.value = false;
  Object.keys(settleForm).forEach((key) => {
    settleForm[key] = '';
  });
  settleForm.accountType = 'S';
  bankArea.value = '';
}

function onSelectUnionMcc() {
  uni.navigateTo({ url: '/pages/picker-view/category/micro-category' });
  emitter.on('picker-category', (item) => {
    form.unionMcc = item.mcc;
    form.unionMccName = item.mccName;
  });
}

function onSelectBankCard() {
  uni.navigateTo({ url: `/pages/settle-info/debit-card/index?isSelect=1` });
  emitter.on('picker-bank-card', (item: any) => {
    isBanEditSettle.value = true;

    item = deepClone(item);
    Object.keys(settleForm).forEach((key) => {
      settleForm[key] = item[key] || settleForm[key];
    });

    // form.bankCardId = item.id;

    bankArea.value = [item.provinceName, item.cityName].join('');

    fileList.value.forEach((file) => {
      item.imageJsonList.forEach((file2) => {
        if (file2?.imageType === file.fileType) {
          file.fileData = file2?.imagePath;
          file.id = file2?.id;
        }
      });
    },
    );
  });
}

function onSelectArea() {
  uni.navigateTo({ url: '/pages/picker-view/area/index' });
  emitter.on('picker-area', (data: any) => {
    const [province, city] = data;
    const areaName = data.map((item: any) => item.areaName).join('');
    if (areaName !== bankArea.value) {
      settleForm.bankBranch = '';
      settleForm.bankChannelNo = '';
    }
    bankArea.value = areaName;
    settleForm.province = province.areaCode;
    settleForm.city = city.areaCode;
  });
}

function onSelectBankType() {
  uni.navigateTo({ url: `/pages/picker-view/bank/bank-type` });
  emitter.on('picker-bank-type', (data: any) => {
    if (settleForm.typeCode !== data.typeCode) {
      settleForm.bankBranch = '';
      settleForm.bankChannelNo = '';
    }
    settleForm.bankName = data.typeName;
    settleForm.typeCode = data.typeCode;
  });
}

function onSelectBankSub() {
  const { typeCode, city, province } = settleForm;
  if (!city)
    return Toast('请选择开户行所在地');
  if (!typeCode)
    return Toast('请选择开户行总行');

  const query = {
    typeCode,
    provinceCode: province,
    cityCode: city,
  };
  uni.navigateTo({
    url: `/pages/picker-view/bank/bank-sub?where=${encodeURIComponent(JSON.stringify(query))}`,
  });

  emitter.on('picker-bank-sub', (data: any) => {
    settleForm.bankBranch = data.bankName;
    settleForm.bankChannelNo = data.clearChannelNo;
  });
}
</script>
