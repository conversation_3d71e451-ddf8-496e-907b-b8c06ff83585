<template>
  <view class="h-full overflow-hidden bg-primary">
    <page-paging ref="pagingRef" refresher-only @on-refresh="queryDataSource">
      <!-- 头部 -->
      <template #top>
        <view class="mt-12px px-15px">
          <text class="text-30rpx">
            请选择合作方
          </text>
        </view>
      </template>

      <!-- 列表 -->
      <view class="p-15px">
        <wd-checkbox-group v-model="checkedStoreNameMccDTOList" cell border>
          <view v-for="(item, key) in datasource" :key="key" class="mb-12px rounded-lg bg-white py-15px">
            <view class="flex items-center">
              <!-- left -->
              <view class="flex shrink-0 flex-col items-center justify-center pl-10px pr-15px">
                <view class="h-100rpx w-100rpx flex items-center rounded-full p-12rpx">
                  <view class="h-full w-full flex items-center justify-center rounded-full bg-primary">
                    <image
                      :src="item.channelIcon || require('@/static/images/refresher_loading.gif') "
                      mode="widthFix"
                      class="h-full w-full rounded-full"
                    />
                  </view>
                </view>
                <view class="w-100rpx break-all pb-12rpx text-center text-24rpx">
                  {{ item.channelName }}
                </view>
              </view>
              <!-- center -->
              <view class="flex grow flex-col break-all">
                <text class="font-500">
                  {{ item.chnMerchName }}
                </text>
                <text class="my-5px text-24rpx text-#b51e1e">
                  点击修改店铺名称/行业类型/POS类型
                </text>
                <text class="text-24rpx text-amber">
                  已有 {{ item.channelOpenStoreCount }} 个店铺
                </text>
              </view>
              <!-- right -->
              <view class="shrink-0">
                <wd-checkbox :model-value="item.channelCode" />
              </view>
            </view>
          </view>
        </wd-checkbox-group>
      </view>

      <!-- 底部 -->
      <template #bottom>
        <view class="bg-white px-30rpx pb-40rpx pt-20rpx">
          <wd-button type="primary" size="large" block @click="save">
            下一步
          </wd-button>
        </view>
      </template>
    </page-paging>

    <!-- 挂载点 -->
    <wd-message-box />
  </view>
</template>

<script setup lang="ts">
import { useMessage } from 'wot-design-uni';
import { NavigationHelper } from '@/utils';
import { StoreCertificationApi } from '@/api/store-certification';

const message = useMessage();

// 分页器ref
const pagingRef = ref();

// 列表数据
const datasource = ref<any> ([]);

const checkedStoreNameMccDTOList: any = ref([]);

const merchInfo = ref<any>({});

onLoad((query: any) => {
  const { transferredData, hasTransferredData } = NavigationHelper.getTransferredData(query);

  if (hasTransferredData) {
    merchInfo.value = Object.assign({}, transferredData);
  }

  queryDataSource();
});

onBackPress((options) => {
  if (options.from === 'backbutton') {
    onBeforeBackPress();
    return true;
  }
});

onUnload(() => {
  // #ifdef MP-WEIXIN
  onBeforeBackPress();
  // #endif
});

// 返回处理
function onBeforeBackPress() {
  uni.redirectTo({ url: '/pages/report/merch-report/index' });
}

function save() {
  if (!checkedStoreNameMccDTOList.value.length) {
    message.alert({
      msg: '请选择合作方',
      title: '提示',
    });
    return;
  }

  const storeNameMccDTOList = datasource.value.filter((item: any) => checkedStoreNameMccDTOList.value.includes(item.channelCode));

  NavigationHelper.navigateToWithData('/pages/report/store-certification/modify-business-info', {
    storeNameMccDTOList,
    merchInfo: merchInfo.value,
  });
}

/** 查询数据 */
function queryDataSource() {
  StoreCertificationApi.findStoreNameAndMccList()
    .then((res: any) => {
      const data = res || [];
      datasource.value = data;

      pagingRef.value?.complete();
    })
    .catch(() => {
      pagingRef.value.complete(false);
    });
}
</script>

<style lang="scss" scoped>
:deep(.wd-checkbox-group){
  background-color: transparent !important;

  .wd-checkbox.is-cell-box{
    padding: 0 12px;
  }
}
</style>
