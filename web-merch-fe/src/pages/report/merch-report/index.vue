<template>
  <view class="h-full bg-primary">
    <page-paging ref="pagingRef" v-model="datasource" :auto="false" @query="queryDataSource">
      <!-- 头部搜索栏 -->
      <template #top>
        <wd-navbar
          left-arrow :bordered="false" custom-class="custom-navbar-class"
          safe-area-inset-top
          @click-left="handlePageHome"
        >
          <template #title>
            <view class="search-box">
              <wd-search
                hide-cancel placeholder-left placeholder="请输入商户名称" custom-class="w-full"
                @search="onSearch"
              />
            </view>
          </template>
        </wd-navbar>

        <!-- 状态Tabs -->
        <view class="bg-white">
          <wd-tabs
            v-model="reportRespCode"
            :line-height="0" color="#4d80f0" custom-class="!w-70%"
            @change="onChangeReportRespCode"
          >
            <wd-tab
              v-for="(item, key) in reportRespCodes" :key="key"
              :title="item.label" :name="item.value"
            />
          </wd-tabs>
        </view>
      </template>

      <!-- 列表 -->
      <view class="p-20rpx">
        <view
          v-for="(item, key) in datasource" :key="key"
          class="mb-20rpx rounded-xl bg-white p-26rpx p-t-16rpx shadow"
          @click="onClickItem(item)"
        >
          <view class="mb-20rpx flex items-center border border-#f3f5f7 border-b-dashed pb-10rpx">
            <view class="h-72rpx w-72rpx flex items-center justify-center rounded-50% bg-primary p-4rpx text-0">
              <image
                :src="findChannelIcon(item.channelCode)"
                mode="widthFix"
                class="h-full w-full rounded-50%"
              />
            </view>
            <text class="ml-10px break-all text-#555 font-450">
              {{ formatChannelName(item.channelCode) }}
            </text>
          </view>
          <view class="cell-group">
            <view class="cell">
              <text class="cell-label">
                商户名称:
              </text>
              <text class="cell-value">
                {{ item.chnMerchName }}
              </text>
            </view>
            <view class="cell">
              <text class="cell-label">
                商户编号:
              </text>
              <view class="cell-value">
                <text>{{ item.chnMerchNo || '--' }}</text>
                <i
                  v-if="!!item.chnMerchNo"
                  class="i-mdi-content-copy ml-10rpx size-28rpx text-#b51e1e"
                  @click.stop="copyMerchNo(item.chnMerchNo)"
                />
              </view>
            </view>
            <view class="cell">
              <text class="cell-label">
                商户类型:
              </text>
              <view class="cell-value">
                <text>{{ item.isMicro === 1 ? '小微' : '企业' }}</text>
                <view class="h-full flex grow items-center justify-end">
                  <!-- 报备状态 -->
                  <wd-tag custom-class="custom-tag-class">
                    {{ item.reportRespCodeDesc }}
                  </wd-tag>
                  <i class="i-mdi-chevron-right text-40rpx text-#00000040" />
                </view>
              </view>
            </view>
            <view v-if="item.termSn" class="cell">
              <text class="cell-label">
                终端SN:
              </text>
              <view class="cell-value">
                <text>{{ item.termSn || '--' }}</text>
                <i
                  v-if="!!item.termSn"
                  class="i-mdi-content-copy ml-10rpx size-28rpx text-#b51e1e"
                  @click.stop="copyMerchNo(item.termSn)"
                />
              </view>
            </view>
            <view class="cell">
              <text class="cell-label">
                创建时间:
              </text>
              <text class="cell-value">
                {{ item.createTime }}
              </text>
            </view>
            <view v-if="item.reportRespCode === 3 && item.reportRespMsg" class="cell !items-start">
              <text class="cell-label">
                失败原因:
              </text>
              <text class="cell-value">
                {{ item.reportRespMsg }}
              </text>
            </view>
          </view>

          <!-- 渠道开通状态 -->
          <view
            v-if="item.reportRespCode === 2"
            class="mt-20rpx"
          >
            <wd-tag custom-class="custom-status-class" mark @click.stop="showErrorMsg('union', item.unionpayOpenStatus === 3, item.unionMessage)">
              <view class="flex items-center">
                <text>
                  {{ formatTagDesc('union', item.unionpayOpenStatus) }}
                </text>
                <view v-if="item.unionpayOpenStatus === 3" class="ml-1px">
                  <wd-icon name="help-circle" size="28rpx" />
                </view>
              </view>
            </wd-tag>
            <wd-tag custom-class="custom-status-class" mark @click.stop="showErrorMsg('wechat', item.wechatOpenStatus === 3, item.wechatMessage)">
              <view class="flex items-center">
                <text>
                  {{ formatTagDesc('wechat', item.wechatOpenStatus) }}
                </text>
                <view v-if="item.wechatOpenStatus === 3" class="ml-1px">
                  <wd-icon name="help-circle" size="28rpx" />
                </view>
              </view>
            </wd-tag>
            <wd-tag custom-class="custom-status-class" mark @click.stop="showErrorMsg('alipay', item.alipayOpenStatus === 3, item.alipayMessage)">
              <view class="flex items-center">
                <text>
                  {{ formatTagDesc('alipay', item.alipayOpenStatus) }}
                </text>
                <view v-if="item.alipayOpenStatus === 3" class="ml-1px">
                  <wd-icon name="help-circle" size="28rpx" />
                </view>
              </view>
            </wd-tag>
          </view>

          <!-- 操作 -->
          <view
            v-if="item.reportRespCode === 2"
            class="mt-20rpx flex flex-wrap items-center justify-end border border-#f3f5f7 border-t-solid pt-20rpx"
          >
            <view class="space">
              <wd-tag plain round custom-class="custom-tool-tag">
                详情
              </wd-tag>
            </view>
            <view v-if="item.wechatOpenStatus === 1 && !item.wechatAuthStatus" class="space" @click.stop="handleScanAuthWechat(item)">
              <wd-tag plain round custom-class="custom-tool-tag">
                微信授权
              </wd-tag>
            </view>
            <view v-if="item.alipayOpenStatus === 1 && !item.alipayAuthStatus" class="space" @click.stop="handleScanAuthAlipay(item)">
              <wd-tag plain round custom-class="custom-tool-tag">
                支付宝授权
              </wd-tag>
            </view>
            <view v-if="item.unionpayOpenStatus === 1" class="space" @click.stop="toBindTerminal(item)">
              <wd-tag plain round custom-class="custom-tool-tag">
                终端绑定
              </wd-tag>
            </view>
            <!-- <view class="space" @click.stop="toBindBlankTerminal(item)">
              <wd-tag plain round custom-class="custom-tool-tag">
                白板机绑定
              </wd-tag>
            </view> -->
            <view v-if="item.isMicro === 1 && item.unionpayOpenStatus === 3" class="space" @click.stop="handleUpgradeCorporate(item)">
              <wd-tag plain round custom-class="custom-tool-tag">
                重新报备
              </wd-tag>
            </view>
          </view>

          <view
            v-if="item.reportRespCode === 3"
            class="mt-20rpx flex flex-wrap items-center justify-center border border-#f3f5f7 border-t-solid pt-20rpx"
          >
            <view class="space">
              <wd-tag plain round custom-class="custom-error-tag">
                驳回重提
              </wd-tag>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部 -->
      <template #bottom>
        <view class="flex items-center bg-white px-40rpx pb-40rpx pt-20rpx">
          <view class="grow">
            <wd-button type="primary" block size="large" @click="addMerch">
              新增商户
            </wd-button>
          </view>
          <view v-if="showOpenD0Btn" class="ml-30rpx shrink-0">
            <wd-button size="large" plain block type="warning" @click="handleOpenD0">
              开通D0
            </wd-button>
          </view>
        </view>
      </template>
    </page-paging>

    <!-- 挂载点 -->
    <wd-message-box />

    <!-- 扫码授权弹窗 -->
    <ScanAuthAlipay v-model:visible="showScanAuthAlipay" :data="authInfo" />
    <ScanAuthWechat v-model:visible="showScanAuthWechat" :data="authInfo" />
  </view>
</template>

<script setup lang="ts">
import { isNumber } from 'wot-design-uni/components/common/util';
import { useMessage } from 'wot-design-uni';
import ScanAuthAlipay from './modules/scan-auth-alipay.vue';
import ScanAuthWechat from './modules/scan-auth-wechat.vue';

import { useClipboard } from '@/hooks';
import { MerchReportApi } from '@/api/report';
import { CHANNEL_CODE } from '@/config/setting';
import { Toast, buildUrlWithParams } from '@/utils';
import { CommonApi } from '@/api/common';

// 分页器ref
const pagingRef = ref();

const message = useMessage();

// 列表查询条件
const where = reactive<any>({
  // channelCode: CHANNEL_CODE,
  chnMerchName: '',
});

// 列表数据
const datasource = ref<Record<string, any>[]> ([]);

const showOpenD0Btn = ref(false);

const channelList = ref<any>([]);

const reportRespCode = ref('0');
const reportRespCodes = [
  { label: '全部', value: '0' },
  { label: '有效', value: '2' },
  { label: '审核中', value: '1' },
  { label: '未通过', value: '3' },
];
const reportRespCodesMap: Record<string, any> = {
  1: '审核中',
  2: '入网成功',
  3: '审核未通过',
  99: '已关闭',
  10: '电子协议待签署',
  15: '待用户选择结算卡',
};

// 授权信息
const authInfo = ref<{
  authImgUrl: string; // 授权图片地址
  merchId: string; // 子商户号
}>();

// 是否显示扫码授权弹窗
const showScanAuthAlipay = ref(false);
const showScanAuthWechat = ref(false);

// 通道开通状态映射
const channelOpenStatusMap: Record<number, string> = {
  '-1': '暂不支持',
  '0': '未开通',
  '1': '已开通',
  '2': '开通中',
  '3': '开通失败',
};
// 银联开通状态映射
const unionpayOpenStatusMap: Record<number, string> = {
  '-1': '暂不支持',
  '0': '未开通',
  '1': '已开通',
  '2': '开通中',
  '3': '开通失败',
  '4': '开通中',
  '11': '开通中',
};

onMounted(() => {
  getChannelList();
  pagingRef.value.reload();
  // getD0AuthUrl();
});

onShow(() => {
  pagingRef.value?.reload();
});

// onBackPress((options) => {
//   if (options.from === 'backbutton') {
//     handlePageHome();
//     return true;
//   }
// });

onUnload(() => {
  setTimeout(() => {
    handlePageHome();
  }, 200);
});

/** 格式化通道名称 */
function formatChannelName(channelCode: string) {
  const channel = channelList.value.find((item: any) => item.channelCode === channelCode);
  return channel?.channelName || '--';
}

/** 查找通道图标 */
function findChannelIcon(channelCode: string) {
  const channel = channelList.value.find((item: any) => item.channelCode === channelCode);
  return channel?.chnIcon || '';
}

async function getChannelList() {
  const data = await CommonApi.getChannelList();
  channelList.value = data || [];
}

async function getD0AuthUrl() {
  const data = await MerchReportApi.d0SettleApply({
    channelCode: CHANNEL_CODE,
  });

  showOpenD0Btn.value = data?.display || false;
}

async function handleOpenD0() {
  const data = await MerchReportApi.d0SettleOpen({
    channelCode: CHANNEL_CODE,
  });

  // #ifdef MP-WEIXIN
  const url = buildUrlWithParams('/pages/report/merch-report/merch-open-d0', {
    authUrl: data.authUrl,
  });
  uni.navigateTo({ url });
  // #endif

  // #ifdef WEB
  message
    .confirm({
      msg: '需要进行人脸实名认证，是否跳转？',
      title: 'D0开通',
    }).then(() => {
      const url = buildUrlWithParams('/pages/common/webview/index', {
        url: data.authUrl,
      });
      // #ifdef WEB
      location.href = data.authUrl;
      // #endif
      // #ifdef MP-WEIXIN
      uni.navigateTo({ url });
      // #endif
    });
  // #endif
}

type ChannelType = 'union' | 'wechat' | 'alipay';
function formatTagDesc(type: ChannelType, status: number) {
  if (status === null) {
    return '--';
  }
  switch (type) {
    case 'union':
      return `银联${unionpayOpenStatusMap[status]}`;
    case 'wechat':
      return `微信${channelOpenStatusMap[status]}`;
    case 'alipay':
      return `支付宝${channelOpenStatusMap[status]}`;
    default:
      return '--';
  }
}

function showErrorMsg(type: ChannelType, hasError: boolean, msg: string) {
  if (!hasError) {
    return;
  }
  let title = '';
  const errorMsg = msg || '开通失败';

  switch (type) {
    case 'union':
      title = '银联';
      break;
    case 'wechat':
      title = '微信';
      break;
    case 'alipay':
      title = '支付宝';
      break;
    default:
      title = '';
      break;
  }

  title += '失败说明';
  message.alert({
    msg: errorMsg,
    title,
  });
}

/** 查询数据 */
function queryDataSource(pageNo: number, pageSize: number) {
  MerchReportApi.findPage({ ...where, pageNo, pageSize })
    .then((res) => {
      res?.rows?.forEach((item: any) => {
        // 处理报备状态
        item.reportRespCodeDesc = reportRespCodesMap[item.reportRespCode];
      });
      pagingRef.value.completeByTotal(res?.rows, res?.totalRows);
    })
    .catch(() => {
      pagingRef.value.completeByTotal(false);
    });
}

/** 根据状态查询数据 */
function onChangeReportRespCode({ name }: { name: string }) {
  where.reportRespCode = name === '0' ? null : Number(name);
  pagingRef.value.reload();
}

/** 搜索查询数据 */
function onSearch({ value }: any) {
  where.chnMerchName = value;
  pagingRef.value?.reload();
}

/**
 * 重新报备
 */
function handleUpgradeCorporate(item: any) {
  message
    .confirm({
      msg: `报备失败原因: ${item.unionMessage || '具体原因未知, 有疑问请联系管理员'}`,
      title: '温馨提示',
      confirmButtonText: '升级为标准商户',
      cancelButtonText: '关闭',
    })
    .then(async () => {
      const data = await MerchReportApi.merchAudit({
        channelCode: item.channelCode, // 通道编号  必填
        chnMerchNo: item.chnMerchNo, // 通道商户编号  必填
        modifyType: 2, // 信息修改类型  必填 1-修改商户简称 2-小微转企业
      });

      if (isNumber(data?.auditStatus)) {
        switch (data?.auditStatus) {
          case 0:
          case 1:
            message.alert({
              msg: '申请已提交, 请您耐心等待',
              title: '温馨提示',
            });
            break;
          case 2:
            message.alert({
              msg: '审核成功啦~',
              title: '温馨提示',
            });
            break;
          case 3:
            message.confirm({
              msg: data?.auditRemark || '审核失败了, 请您重新提交~',
              title: '审核失败',
              confirmButtonText: '重新提交',
            }).then(() => {
              const url = buildUrlWithParams('/pages/report/merch-report/upgrade-corporate-merch', {
                resubmit: '1',
                chnMerchNo: item.chnMerchNo,
                shortName: item.chnMerchName,
              });
              uni.navigateTo({ url });
            });
            break;
        }
        return;
      }

      const url = buildUrlWithParams('/pages/report/merch-report/upgrade-corporate-merch', {
        chnMerchNo: item.chnMerchNo,
        shortName: item.chnMerchName,
      });
      uni.navigateTo({ url });
    })
    .catch(() => {
      console.log('cancel');
    });
}

/**
 * 点击列表项
 */
function onClickItem(item: any) {
  switch (item.reportRespCode) {
    case 2:
      // 详情
      uni.navigateTo({ url: `/pages/report/merch-report/merch-report-detail?id=${item.id}` });
      break;
    case 3:
      // 驳回重提
      resubmitMerch(item);
      break;
    default:
      break;
  }
}

async function handleScanAuthAlipay(item: any) {
  const data = await MerchReportApi.chnAuthQrcode({
    channelCode: item.channelCode,
    payMethod: 3,
    chnMerchNo: item.chnMerchNo,
  });
  authInfo.value = {
    authImgUrl: data.imageUrl,
    merchId: data.alipayMerchId || '',
  };
  showScanAuthAlipay.value = true;
}

async function handleScanAuthWechat(item: any) {
  const data = await MerchReportApi.chnAuthQrcode({
    channelCode: item.channelCode,
    payMethod: 2,
    chnMerchNo: item.chnMerchNo,
  });
  authInfo.value = {
    authImgUrl: data.imageUrl,
    merchId: data.wechatMerchId || '',
  };
  showScanAuthWechat.value = true;
}

function copyMerchNo(data: string) {
  useClipboard().setClipboardData({ data });
}

function addMerch() {
  uni.navigateTo({ url: '/pages/report/merch-dredge/index?handleType=2' });
}

function resubmitMerch(item: any) {
  uni.navigateTo({ url: `/pages/report/merch-dredge/index?handleType=3&id=${item.id}&isMicro=${item.isMicro}` });
}

function toBindTerminal(item: any) {
  uni.navigateTo({ url: `/pages/terminal/index?chnMerchNo=${item.chnMerchNo}&channelCode=${item.channelCode}` });
}

function toBindBlankTerminal(item: any) {
  uni.navigateTo({ url: `/pages/report/merch-report/blank-terminal-bind?chnMerchNo=${item.chnMerchNo}&channelCode=${item.channelCode}` });
}

function handlePageHome() {
  uni.switchTab({ url: '/pages/tab/home/<USER>' });
}
</script>

<style lang="scss" scoped>
.search-box {
  display: flex;
  align-items: center;
  height: 100%;
  text-align: left;

  --wot-search-padding: 0;
  --wot-search-side-padding: 0;
}

.cell-group{
  .cell{
    @apply flex items-center;

    &:not(:last-child){
     @apply mb-8rpx
    }

     .cell-label{
      @apply shrink-0;
     }

     .cell-value{
      @apply grow ml-20rpx flex items-center text-#333;
     }
  }
}

:deep(.custom-tag-class){
  margin-right: 10rpx;
  font-size: 26rpx !important;
  color: #4d80f0 !important;
  background: #d0e8ff !important;
}

:deep(.custom-navbar-class){
  padding-top: 8px;

  .wd-navbar__title{
    padding: 0 12px 0 44px;
    margin: 0;
    max-width: 100%;
  }
}

:deep(.space){
  .custom-tool-tag{
  padding: 4px 0 !important;
  margin-left: 10rpx;
  width: 140rpx;
  font-size: 24rpx !important;
  text-align: center;
  border-radius: 4rpx;
  }

  &:nth-last-child(1){
    .custom-tool-tag{
      color: #4d80f0 ;
      border-color: #4d80f0 ;
    }
   }

   .custom-error-tag{
      padding: 0 !important;
      font-size: 28rpx !important;
      color: #e33743 !important;
      border:none !important;
}

}

:deep(.custom-status-class){
  margin-right: 10rpx;
  font-size: 25rpx !important;
  color: #4d80f0 !important;
  background: #d0e8ff !important;

  &:nth-last-child(1){
    margin-right: 0;
  }
}
</style>
