<template>
  <view class="h-full flex flex-col bg-primary">
    <view v-if="!isEmbeApplet" class="shrink-0">
      <wd-navbar title="实名认证" safe-area-inset-top left-arrow :bordered="false" @click-left="handleClickLeft" />
    </view>

    <scroll-view
      scroll-y :show-scrollbar="false" :scroll-top="scrollTop" :throttle="false"
      class="mt-10px grow"
    >
      <view class="bg-white py-12px">
        <view class="mb-12px px-12px font-bold">
          人脸照
        </view>
        <view v-for="(item, key) in [fileMap[5]]" :key="key" class="w-50%">
          <GivenUpload
            v-model:file-data="item.fileData"
            :file-name="item.fileName"
            :placeholder="item.placeholder"
            custom-class="w-full"
          />
        </view>
      </view>

      <view class="mt-10px bg-white py-12px">
        <view class="mb-12px px-12px font-bold">
          身份证信息
        </view>
        <view class="flex">
          <view v-for="(item, key) in [fileMap[1], fileMap[2]]" :key="key" class="w-50%">
            <GivenUpload
              v-model:file-data="item.fileData"
              :file-name="item.fileName"
              :placeholder="item.placeholder"
              custom-class="w-full"
              @choose="(file) => ocrIdcardByFile(file, item)"
            />
          </view>
        </view>
      </view>

      <wd-form ref="formRef" :model="form" :rules="rules" custom-class="error-message__align-right">
        <wd-cell-group border>
          <wd-input
            v-model="form.legalName" prop="legalName"
            label="姓名" placeholder="自动识别" label-width="100px"
            align-right
          />
          <wd-input
            v-model="form.legalCertNo" prop="legalCertNo"
            label="身份证号" placeholder="自动识别" label-width="100px"
            align-right
          />
          <wd-textarea
            v-model="form.legalAddr" prop="legalAddr"
            label="居住地址" placeholder="请输入住所或工作单位地址" label-width="100px"
            auto-height
            custom-textarea-class="text-right"
          />
          <wd-datetime-picker
            v-model="licenseDateRegion" prop="legalCertEndDate"
            label="证件有效期" label-width="100px"
            type="date" :min-date="minDate" :max-date="maxDate" align-right
            :default-value="defaultDateRegionValue"
            @confirm="onConfirmLicenseDateRegion"
          />
          <wd-cell
            title="商户地区" title-width="100px"
            prop="city" is-link
            @click="onSelectArea"
          >
            <wd-textarea
              v-model="bankArea"
              placeholder="请选择省/市/区"
              auto-height no-border readonly
              custom-textarea-class="text-right"
            />
          </wd-cell>
          <wd-cell
            title="详细地址" title-width="100px"
            prop="positionAddr" is-link
            @click="onSelectAddress"
          >
            <wd-textarea
              v-model="form.positionAddr"
              placeholder="商户注册地址"
              auto-height no-border readonly
              custom-textarea-class="text-right"
            />
          </wd-cell>
        </wd-cell-group>

        <view class="p-40rpx">
          <wd-button type="primary" size="large" block @click="onDebounceSave">
            提交
          </wd-button>
        </view>
      </wd-form>
    </scroll-view>

    <wd-toast />
    <wd-message-box />
  </view>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'wot-design-uni/components/wd-form/types';
import { useMessage, useToast } from 'wot-design-uni';
import dayjs from 'dayjs';
import { debounce } from 'wot-design-uni/components/common/util';
import jsweixin from 'weixin-js-sdk';
import type { GivenUploadProps } from '@/components/given-upload/type';
import { CommonApi } from '@/api/common';
import { CHANNEL_CODE } from '@/config/setting';
import { emitter } from '@/utils/emitter';
import { MerchReportApi } from '@/api/report';
import { MerchApi } from '@/api/merch';
import { buildUrlWithParams, setToken } from '@/utils';

const toast = useToast();
const message = useMessage();

const isEmbeApplet = ref(false);

// 表单
const formRef = ref<FormInstance | null>(null);
const form = reactive<any>({
  // 证件信息
  legalCertNo: '', // 证件号码 必填，用来与之前的比对 是否一致
  legalName: '', // 姓名 必填
  legalAddr: '', // 地址 必填
  legalSex: '', // 性别 必填
  legalCertStartDate: '', // 证件有效期（开始：yyyy-MM-dd）必填
  legalCertEndDate: '', // 证件有效期（结束：yyyy-MM-dd） 必填

  // 经营地址
  province: '', // 省编码
  city: '', // 市编码
  country: '', // 区县 编码
  positionAddr: '', //* 街道地址/定位地址（省+市+街道定位）
  longitude: '', // * 经度（地理位置x）
  latitude: '', // * 纬度（地理位置y）

  imageJsonList: [],
});
// 规则
const idCardPattern = /(^\d{15}$)|(^\d{18}$)|(^\d{17}([\dX])$)/i;
const validatorPositionAddr = (val: string) => {
  if (String(val).length >= 7) {
    return Promise.resolve();
  }
  else {
    return Promise.reject();
  }
};
const rules: FormRules = {
  legalCertNo: [{ required: true, pattern: idCardPattern, message: '请正确填写证件号码' }],
  legalName: [{ required: true, message: '请填写姓名' }],
  legalAddr: [{ required: true, message: '请填写居住地址' }],
  legalCertEndDate: [{ required: true, message: '请选择证件有效期' }],
  city: [{ required: true, message: '请选择商户地区' }],
  positionAddr: [{ required: true, validator: validatorPositionAddr, message: '详细地址不能少于七个字' }],
};

const licenseDateRegion = ref<any>([]);
const defaultDateRegionValue = ref([dayjs().valueOf(), dayjs().valueOf()]);

const minDate = dayjs('1900-01-01').valueOf();
const maxDate = dayjs('2099-12-31').valueOf();

// 地区文本
const bankArea = ref('');

const cityName = ref('');
const adName = ref('');

const scrollTop = ref(0);

// 文件
type FileType = 1 | 2 | 5 ;
const fileMap = ref <Record<FileType, GivenUploadProps>> ({
  1: {
    fileName: '人像面',
    placeholder: require('@/static/images/card_face.png'),
    fileData: '',
    fileType: 1,
  },
  2: {
    fileName: '国徽面',
    placeholder: require('@/static/images/card_back.png'),
    fileData: '',
    fileType: 2,
  },
  5: {
    fileName: '人脸照',
    placeholder: require('@/static/images/card_hand.png'),
    fileData: '',
    fileType: 5,
  },
});

function handleClickLeft() {
  message
    .confirm({
      msg: '返回当前页面内容将丢失哦, 是否确定返回?',
      title: '温馨提示',
    })
    .then(() => {
      uni.navigateBack();
    });
}

onLoad((options) => {
  if (options?.isResubmit === '1') {
    queryResubmitInfo();
  }

  if (options?.client_type === 'applet') {
    isEmbeApplet.value = true;
  }

  if (options?.token) {
    setToken(options.token);
  }
});

onShow(() => {
  if (isEmbeApplet.value) {
    document.title = '实名认证';
  }
});

async function queryResubmitInfo() {
  const res = await MerchApi.queryMerchInfo();
  Object.keys(form).forEach((key) => {
    form[key] = res?.merchantDetail[key] || res?.merchant[key] || form[key];
  });

  const { provinceName, cityName: cityname, countryName } = res?.merchant || {};
  bankArea.value = [provinceName, cityname, countryName].join('');
  adName.value = countryName;
  cityName.value = cityname;

  const { legalCertStartDate, legalCertEndDate } = res?.merchantDetail || {};
  if (legalCertEndDate && legalCertStartDate) {
    licenseDateRegion.value = [dayjs(legalCertStartDate).valueOf(), dayjs(legalCertEndDate).valueOf()];
  }

  if (res?.imageList?.length) {
    const fileList = Object.values(fileMap.value);
    fileList.forEach((item) => {
      res?.imageList.forEach((item2) => {
        if (item2?.imageType === item.fileType) {
          item.fileData = item2?.imagePath;
          item.id = item2?.id;
        }
      });
    });
  }
}

const onDebounceSave = debounce(() => {
  save();
}, 500);
async function save() {
  // 检验表单
  const { valid, errors } = await formRef.value!.validate();
  if (!valid)
    return Promise.reject(errors);

  // 校验文件
  await checkFile();
  // 上传文件
  const imageList = await uploadFile();
  form.imageJsonList = imageList as any;

  const data = await MerchReportApi.auth(form);
  switch (data?.code) {
    case '00':
      toast.success({
        msg: '认证通过',
        closed: () => {
          const url = '/pages/report/merch-dredge/index?handleType=1';
          if (isEmbeApplet.value) {
            jsweixin.miniProgram.reLaunch({ url });
          }
          else {
            uni.reLaunch({ url });
          }
        },
      });
      break;
    case '01':
      toast.error({ msg: '认证失败' });
      break;
    case '02':
      // eslint-disable-next-line no-case-declarations
      const url = '/pages/report/merch-auth/auth-result';
      if (isEmbeApplet.value) {
        jsweixin.miniProgram.redirectTo({ url });
      }
      else {
        uni.redirectTo({ url });
      }
      break;
  }
}

function onSelectAddress() {
  if (!cityName.value) {
    toast.warning('请选择商户地区');
    return;
  }
  const url = buildUrlWithParams('/pages/report/merch-auth/map-poi', {
    c: cityName.value,
    embe: isEmbeApplet.value ? '1' : '0',
  });
  uni.navigateTo({ url });

  emitter.on('picker-address', async (item) => {
    form.positionAddr = item.address;
    form.longitude = item.longitude;
    form.latitude = item.latitude;

    if (item.adname && item.adname !== adName.value) {
      const countrys = await queryCountry(form.city);
      const newItem = countrys.find((i: any) => i.areaName === item.adname);
      if (newItem) {
        form.country = newItem.areaCode;
        bankArea.value = bankArea.value.replace(adName.value, item.adname);
        adName.value = item.adname;
      }
    }
  });
}

/**
 * 查询区
 */
async function queryCountry(parentCode: string) {
  const data = await CommonApi.queryCountry({ parentCode });
  return data || [];
}

/**
 * 上传文件
 */
async function uploadFile() {
  const fileList = Object.values(fileMap.value);
  const fileListHasVal = fileList.filter(i => !!i.fileData);
  const noChangeFiles = [];
  const changeedFiles = [];
  fileListHasVal.forEach((i) => {
    if (/^(https?:)/.test(i.fileData)) {
      noChangeFiles.push({
        id: i.id,
        imageType: i.fileType,
        imagePath: i.fileData,
      });
    }
    else {
      changeedFiles.push({
        fileType: i.fileType,
        suffixType: 'png',
        fileData: i.fileData,
      });
    }
  });

  let imageJsonList = [];
  if (changeedFiles.length) {
    const data = await CommonApi.uploadImages({ channelCode: CHANNEL_CODE, imageList: changeedFiles })
      .catch(() => Promise.reject(new Error('upload fail!')));
    imageJsonList = data?.imageJsonList;
  }

  return [...noChangeFiles, ...imageJsonList];
}

/*
 * 校验文件
 */
async function checkFile() {
  const fileList = Object.values(fileMap.value);
  const isPass = fileList.every((f) => {
    if (!f.fileData) {
      toast.warning(`请上传${f.fileName}`);
      return false;
    }
    return true;
  });
  if (!isPass) {
    return Promise.reject(new Error('upload check fail !'));
  }
  return Promise.resolve();
}

function onSelectArea() {
  uni.navigateTo({ url: '/pages/picker-view/area/index?leaf=3' + `&embe=${isEmbeApplet.value ? '1' : '0'}` });
  emitter.on('picker-area', (data: any) => {
    const [province, city, country] = data;
    if (country.areaCode !== form.country) {
      form.positionAddr = '';
    }
    bankArea.value = data.map((item: any) => item.areaName).join('');
    cityName.value = city.areaName;
    adName.value = country.areaName;
    form.province = province.areaCode;
    form.city = city.areaCode;
    form.country = country.areaCode;
  });
}

function onConfirmLicenseDateRegion() {
  const [startDate, endDate] = licenseDateRegion.value;
  form.legalCertStartDate = dayjs(startDate).format('YYYY-MM-DD');
  form.legalCertEndDate = dayjs(endDate).format('YYYY-MM-DD');
}

/** ocr身份证 */
async function ocrIdcardByFile(file: string, item: any) {
  const { fileType } = item;
  const side = fileType === 1 ? 'face' : 'back';
  const data = await CommonApi.ocrIdCard({ imgFile: file, side });

  if (!data?.success)
    return;

  switch (side) {
    case 'face':
      form.legalName = data.name;
      form.legalCertNo = data.num;
      form.legalAddr = data.address;
      form.legalSex = data.sex;
      break;
    case 'back':
      // eslint-disable-next-line no-case-declarations
      let { startDate, endDate } = data;
      if (startDate && endDate) {
        if (endDate === '长期') {
          endDate = '2099-12-31';
        }
        licenseDateRegion.value = [dayjs(startDate).valueOf(), dayjs(endDate).valueOf()];
        onConfirmLicenseDateRegion();
      }
      break;
  }
}
</script>
