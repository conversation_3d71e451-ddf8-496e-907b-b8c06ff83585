<template>
  <view class="h-full flex flex-col bg-primary">
    <view v-if="showNavbar" class="shrink-0">
      <wd-navbar title="详细地址" safe-area-inset-top left-arrow @click-left="handleClickLeft" />
    </view>
    <view class="shrink-0">
      <wd-textarea v-model="searchKeyword" placeholder="详细地址 (例如**街**号**)" @input="onSearch" />
      <!-- <wd-textarea v-if="current" v-model="replenishValue" label="地址补充:" label-width="80px" placeholder="补充地址信息" auto-height /> -->
      <!-- <view class="flex justify-end bg-white p-20rpx">
        <template v-if="!!searchKeyword">
          <view>
            <wd-button type="info" size="small" plain block @click="onClear">
              清空
            </wd-button>
          </view>
          <view v-if="current" class="ml-20rpx">
            <wd-button size="small" block @click="onConfirm">
              确定
            </wd-button>
          </view>
        </template>
        <view v-else>
          <wd-button type="error" size="small" plain block @click="onCancel">
            取消
          </wd-button>
        </view>
      </view> -->
    </view>
    <view v-if="prompts?.length" class="p-20rpx">
      <text class="text-26rpx text-#999">
        请选择地址
      </text>
    </view>
    <view class="grow overflow-y-scroll">
      <view
        v-for="(item, key) in prompts" :key="key"
        class="flex flex-col border border-#edf0f3 border-b-solid bg-white p-20rpx"
        @click="onSelect(item)"
      >
        <text class="text-32rpx font-600" v-html="formatTextStyle(item.name)" />
        <text class="mt-10rpx text-#999">
          {{ item.address }}
        </text>
      </view>
    </view>
    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import { useMessage } from 'wot-design-uni';
import AMapLoader from '@amap/amap-jsapi-loader';
import { debounce } from 'wot-design-uni/components/common/util';
import { MAP_KEY, MAP_SECURITY_CODE } from '@/config/setting';
import { emitter } from '@/utils/emitter';

/**
 * 安全密钥配置
 */
window._AMapSecurityConfig = {
  securityJsCode: MAP_SECURITY_CODE,
};

const message = useMessage();

const prompts = ref<any>([]);

const replenishValue = ref();

const cityName = ref('');

const current = ref<any>(null);

let autoComplete: any = null;

const showNavbar = ref(true);

// 地址搜索关键字
const searchKeyword = ref<string>('');
const isDisabledSearch = computed(() => {
  return !!current.value || !cityName.value;
});

onLoad((options) => {
  if (!options?.c) {
    message.alert('城市不能为空');
    return;
  }
  if (options?.embe) {
    showNavbar.value = options?.embe !== '1';
  }

  cityName.value = options?.c;
  initAmap();
});

onUnmounted(() => {
  emitter.off('picker-address');
});

function handleClickLeft() {
  uni.navigateBack();
}
const onSearch = debounce(({ value }) => {
  autoComplete.search(value, (status, result) => {
    if (status === 'complete' && result.info === 'OK') {
      if (result?.tips) {
        // 过滤 只取有详细地址的项
        const filterTips = result.tips.filter(t => typeof t.address === 'string' && !!t.address);
        prompts.value = filterTips;
      }
    }
    else {
      prompts.value = [];
    }
  });
}, 500);

/**
 * 初始化地图插件
 */
function initAmap() {
  AMapLoader.load({
    key: MAP_KEY,
    version: '2.0',
    plugins: ['AMap.AutoComplete'],
  })
    .then((AMap) => {
      const autoOptions = {
        city: cityName.value, // 限定城市
        citylimit: true, // 只查当前城市
      };
      autoComplete = new AMap.Autocomplete(autoOptions);
    })
    .catch((e) => {
      console.log(e);
    });
}

function onSelect(item: any) {
  searchKeyword.value = `${item.address}${item.name}`;
  current.value = item;
  replenishValue.value = '';
  onConfirm();
}

function onConfirm() {
  const resBody = {
    address: `${current.value.address}`,
    longitude: current.value.location?.lng,
    latitude: current.value.location?.lat,
    adname: current.value.district.split('市')[1],
  };
  emitter.emit('picker-address', resBody);
  uni.navigateBack();
}

function onCancel() {
  uni.navigateBack();
}

function onClear() {
  searchKeyword.value = '';
  current.value = null;
  prompts.value = [];
  replenishValue.value = '';
}

function formatTextStyle(text: string) {
  const reg = new RegExp(searchKeyword.value, 'g');
  return text.replace(reg, `<text class="text-red">${searchKeyword.value}</text>`);
}
</script>
