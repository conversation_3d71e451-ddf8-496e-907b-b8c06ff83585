<template>
  <view class="h-full overflow-y-scroll">
    <view class="gap-primary" />

    <view class="p-80rpx">
      <view class="flex flex-col items-center">
        <image
          :src="authStatusInfo.icon"
          mode="widthFix"
          class="w-200rpx"
        />
        <text class="my-15px font-bold">
          {{ authStatusInfo.title }}
        </text>
        <text class="text-24rpx text-#666">
          {{ authStatusInfo.desc }}
        </text>
      </view>
    </view>

    <view class="px-80rpx">
      <wd-button v-if="authStatusInfo.status === 1" block @click="onRefrsh">
        刷新
      </wd-button>
      <wd-button v-else-if="authStatusInfo.status === 2" block type="error" @click="onResubmit">
        重新提交
      </wd-button>
      <wd-button v-else-if="authStatusInfo.status === 3" block type="success" @click="onPerfectInfo">
        下一步
      </wd-button>
    </view>
    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import { useMessage } from 'wot-design-uni';
import { MerchApi } from '@/api/merch';

interface IAuthStatus {
  status?: number;
  title?: string;
  icon?: any;
  desc?: string;
}

const message = useMessage();

const authStatusMap: IAuthStatus[] = [
  {
    status: 1,
    title: '已提交, 审核中',
    icon: require('../static/images/shz.png'),
    desc: '正在审核, 请耐心等待 ~ ~',
  },
  {
    status: 2,
    title: '审核不通过',
    icon: require('../static/images/shsb.png'),
    desc: '', // 动态设置失败原因
  },
  {
    status: 3,
    title: '审核通过',
    icon: require('../static/images/shcg.png'),
    // desc: '审核通过啦, 请前往签署协议 ~ ~',
    desc: '审核通过啦, 继续下一步吧~ ~',
  },
];

const authStatusInfo = ref<IAuthStatus>({});

onMounted(() => {
  getResultInfo();
});

/**
 * 获取审核结果
 */
async function getResultInfo(isRefresh = false) {
  const data = await MerchApi.queryStatus();

  if (isRefresh) {
    message.alert({
      msg: '刷新状态成功',
    });
  }
  const { authStatus, authMsg } = data || {};

  switch (authStatus) {
    case 1:
    case 3:
      authStatusInfo.value = authStatusMap[0];
      break;
    case 2:
      authStatusInfo.value = authStatusMap[1];
      authStatusInfo.value.desc = authMsg;
      break;
    case 4:
      authStatusInfo.value = authStatusMap[2];
      break;
  }
}

function onRefrsh() {
  getResultInfo(true);
}

function onResubmit() {
  uni.navigateTo({ url: '/pages/report/merch-auth/auth-micro-merch/index?isResubmit=1' });
}

function onPerfectInfo() {
  uni.navigateTo({ url: '/pages/report/merch-signature/protocol/index' });
  // uni.navigateTo({ url: '/pages/report/merch-dredge/index?handleType=1' });
}
</script>
